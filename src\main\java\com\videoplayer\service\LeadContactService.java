package com.videoplayer.service;

import com.videoplayer.entity.LeadContact;
import com.videoplayer.repository.LeadContactRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 联系信息服务类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional
public class LeadContactService {

    @Autowired
    private LeadContactRepository leadContactRepository;

    /**
     * 获取轮播展示的联系信息
     * @param limit 限制数量，如果为0或负数则返回所有
     * @return 联系信息列表
     */
    public List<LeadContact> getCarouselContacts(int limit) {
        List<LeadContact> allContacts = leadContactRepository.findActiveContactsOrderBySortOrder();

        if (limit <= 0) {
            return allContacts;
        } else {
            return allContacts.size() > limit ? allContacts.subList(0, limit) : allContacts;
        }
    }

    /**
     * 获取所有启用的联系信息
     */
    public List<LeadContact> getAllActiveContacts() {
        return leadContactRepository.findActiveContactsOrderBySortOrder();
    }

    /**
     * 根据ID获取联系信息
     */
    public Optional<LeadContact> getContactById(Long id) {
        return leadContactRepository.findById(id);
    }

    /**
     * 获取默认联系信息
     */
    public Optional<LeadContact> getDefaultContact() {
        return leadContactRepository.findDefaultContact();
    }

    /**
     * 保存联系信息
     */
    public LeadContact saveContact(LeadContact contact) {
        // 如果没有设置排序顺序，自动设置为最大值+1
        if (contact.getSortOrder() == null || contact.getSortOrder() == 0) {
            Integer maxOrder = leadContactRepository.findMaxSortOrder();
            contact.setSortOrder(maxOrder + 1);
        }
        
        return leadContactRepository.save(contact);
    }

    /**
     * 删除联系信息（软删除）
     */
    public void deleteContact(Long id) {
        Optional<LeadContact> contactOpt = leadContactRepository.findById(id);
        if (contactOpt.isPresent()) {
            LeadContact contact = contactOpt.get();
            contact.setIsActive(false);
            leadContactRepository.save(contact);
        }
    }

    /**
     * 设置默认联系信息
     * @param id 要设置为默认的联系信息ID
     */
    public void setDefaultContact(Long id) {
        // 先清除所有默认标记
        List<LeadContact> allContacts = leadContactRepository.findAll();
        for (LeadContact contact : allContacts) {
            if (contact.getIsDefault()) {
                contact.setIsDefault(false);
                leadContactRepository.save(contact);
            }
        }
        
        // 设置新的默认联系信息
        Optional<LeadContact> contactOpt = leadContactRepository.findById(id);
        if (contactOpt.isPresent()) {
            LeadContact contact = contactOpt.get();
            contact.setIsDefault(true);
            leadContactRepository.save(contact);
        }
    }

    /**
     * 根据电话号码查找联系信息
     */
    public Optional<LeadContact> findByPhone(String phone) {
        return leadContactRepository.findByPhoneAndIsActiveTrue(phone);
    }

    /**
     * 根据联系人姓名查找
     */
    public List<LeadContact> findByContactName(String contactName) {
        return leadContactRepository.findByContactNameContainingAndIsActiveTrue(contactName);
    }

    /**
     * 统计启用的联系信息数量
     */
    public long countActiveContacts() {
        return leadContactRepository.countActiveContacts();
    }

    /**
     * 初始化默认联系信息数据
     * 如果数据库中没有联系信息，则创建默认数据
     */
    public void initializeDefaultContacts() {
        long count = countActiveContacts();
        if (count == 0) {
            // 创建默认联系信息
            LeadContact defaultContact = new LeadContact();
            defaultContact.setContactName("林佳");
            defaultContact.setPhone("18722880704");
            defaultContact.setWechat("linjia2024");
            defaultContact.setDouyin("huanglinjia");
            defaultContact.setDouyinNickname("皮皮管理");
            defaultContact.setContactType("主管");
            defaultContact.setIsDefault(true);
            defaultContact.setSortOrder(1);
            saveContact(defaultContact);

            // 创建其他联系信息
            LeadContact contact2 = new LeadContact();
            contact2.setContactName("客服小王");
            contact2.setPhone("************");
            contact2.setWechat("kefu001");
            contact2.setDouyin("kefu_douyin");
            contact2.setDouyinNickname("客服小王");
            contact2.setContactType("客服");
            contact2.setIsDefault(false);
            contact2.setSortOrder(2);
            saveContact(contact2);

            LeadContact contact3 = new LeadContact();
            contact3.setContactName("张三");
            contact3.setPhone("13800138000");
            contact3.setWechat("zhangsan");
            contact3.setDouyin("zhangsan_dy");
            contact3.setDouyinNickname("张三");
            contact3.setContactType("销售");
            contact3.setIsDefault(false);
            contact3.setSortOrder(3);
            saveContact(contact3);
        }
    }
}
