<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化控制栏设计测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    
    <style>
        /* 现代化控制栏样式 */
        .video-js {
            width: 270px !important;
            height: 380px !important;
            background-color: #000;
            max-width: none !important;
            max-height: none !important;
        }

        .video-js video {
            width: 270px !important;
            height: 380px !important;
            object-fit: cover;
        }

        .video-js.vjs-fluid {
            width: 270px !important;
            height: 380px !important;
            padding-top: 0 !important;
        }

        .video-js.vjs-responsive {
            width: 270px !important;
            height: 380px !important;
            max-width: 270px !important;
            max-height: 380px !important;
        }

        .video-js .vjs-big-play-button {
            background-color: rgba(0, 0, 0, 0.45);
            border: 2px solid #fff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            line-height: 60px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            transition: all 0.3s ease;
        }

        .video-js:hover .vjs-big-play-button {
            background-color: rgba(0, 0, 0, 0.65);
            transform: translate(-50%, -50%) scale(1.1);
        }

        /* 现代化控制栏样式 */
        .video-js .vjs-control-bar {
            background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 0 0 8px 8px;
            height: 50px;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        /* 控制栏悬停效果 */
        .video-js:hover .vjs-control-bar {
            background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.9) 100%);
            backdrop-filter: blur(15px);
        }

        /* 播放/暂停按钮 */
        .video-js .vjs-play-control {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-right: 12px;
            transition: all 0.3s ease;
        }

        .video-js .vjs-play-control:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: scale(1.05);
        }

        .video-js .vjs-play-control .vjs-icon-placeholder {
            font-size: 14px;
            line-height: 36px;
        }

        /* 进度条容器 */
        .video-js .vjs-progress-control {
            flex: 1;
            height: 6px;
            margin: 0 12px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .video-js .vjs-progress-holder {
            height: 100%;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.1);
        }

        /* 已播放进度 */
        .video-js .vjs-play-progress {
            background: linear-gradient(90deg, #ff6b6b 0%, #ff8e8e 100%);
            border-radius: 3px;
            position: relative;
        }

        /* 进度条拖拽点 */
        .video-js .vjs-play-progress::before {
            content: '';
            position: absolute;
            right: -6px;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .video-js .vjs-progress-control:hover .vjs-play-progress::before {
            opacity: 1;
        }

        /* 缓冲进度 */
        .video-js .vjs-load-progress {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        /* 时间显示 */
        .video-js .vjs-time-control {
            font-size: 12px;
            font-weight: 500;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            min-width: auto;
            padding: 0 4px;
        }

        .video-js .vjs-current-time,
        .video-js .vjs-duration {
            display: block;
        }

        .video-js .vjs-time-divider {
            color: rgba(255, 255, 255, 0.7);
            margin: 0 2px;
        }

        /* 音量控制 */
        .video-js .vjs-volume-panel {
            width: auto;
            margin-left: 8px;
        }

        .video-js .vjs-mute-control {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .video-js .vjs-mute-control:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .video-js .vjs-mute-control .vjs-icon-placeholder {
            font-size: 12px;
            line-height: 32px;
        }

        /* 音量滑块 */
        .video-js .vjs-volume-control {
            width: 60px;
            height: 32px;
            margin-left: 8px;
        }

        .video-js .vjs-volume-bar {
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 14px 0;
        }

        .video-js .vjs-volume-level {
            background: linear-gradient(90deg, #4ecdc4 0%, #44a08d 100%);
            border-radius: 2px;
        }

        /* 倍速按钮 */
        .video-js .vjs-playback-rate .vjs-playback-rate-value {
            font-size: 11px;
            font-weight: 600;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 4px 8px;
            min-width: 32px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .video-js .vjs-playback-rate:hover .vjs-playback-rate-value {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
        }

        /* 全屏按钮 */
        .video-js .vjs-fullscreen-control {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-left: 8px;
            transition: all 0.3s ease;
        }

        .video-js .vjs-fullscreen-control:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: scale(1.05);
        }

        .video-js .vjs-fullscreen-control .vjs-icon-placeholder {
            font-size: 12px;
            line-height: 32px;
        }

        /* 控制栏隐藏/显示动画 */
        .video-js.vjs-user-inactive .vjs-control-bar {
            opacity: 0;
            transform: translateY(100%);
            transition: all 0.3s ease;
        }

        .video-js.vjs-user-active .vjs-control-bar,
        .video-js:hover .vjs-control-bar {
            opacity: 1;
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        /* 页面样式 */
        body {
            background: #f8f9fa;
            padding: 20px 0;
        }

        .video-player-container {
            position: relative;
            background: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            width: 100%;
            max-width: 310px;
            margin-left: auto;
            margin-right: auto;
            padding: 20px;
        }

        .video-wrapper {
            position: relative;
            width: 270px;
            height: 380px;
            background: #000;
            margin: 0 auto;
            border-radius: 8px;
            overflow: hidden;
        }

        .video-wrapper .video-js {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .feature-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .feature-item h6 {
            color: #007bff;
            margin-bottom: 8px;
        }

        .feature-item p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">现代化控制栏设计</h1>
        
        <div class="test-section">
            <h3 class="text-center mb-3">全新设计的播放器控制栏</h3>
            <p class="text-center text-muted mb-4">现代化、美观、易用的控制栏设计</p>
            
            <!-- 视频播放器容器 -->
            <div class="video-player-container">
                <div class="video-wrapper">
                    <video
                        id="video-player"
                        class="video-js vjs-default-skin vjs-big-play-centered"
                        controls
                        preload="metadata"
                        width="270"
                        height="380"
                        data-setup='{
                            "fluid": false,
                            "responsive": false,
                            "controls": true,
                            "autoplay": false,
                            "preload": "metadata",
                            "playbackRates": [0.5, 0.75, 1, 1.25, 1.5, 2]
                        }'>
                        <source src="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/jyqk-sos-01.mp4" type="video/mp4">
                        <p class="vjs-no-js">
                            要播放此视频，请启用JavaScript，并考虑升级到
                            <a href="https://videojs.com/html5-video-support/" target="_blank">
                                支持HTML5视频的Web浏览器
                            </a>。
                        </p>
                    </video>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h4>🎨 设计特色</h4>
            <div class="feature-list">
                <div class="feature-item">
                    <h6><i class="fas fa-palette me-2"></i>渐变背景</h6>
                    <p>使用透明到黑色的渐变背景，配合毛玻璃效果，更加现代化</p>
                </div>
                <div class="feature-item">
                    <h6><i class="fas fa-circle me-2"></i>圆形按钮</h6>
                    <p>播放、音量、全屏按钮采用圆形设计，带有半透明背景和边框</p>
                </div>
                <div class="feature-item">
                    <h6><i class="fas fa-sliders-h me-2"></i>彩色进度条</h6>
                    <p>进度条使用渐变色彩，拖拽点在悬停时显示，提升交互体验</p>
                </div>
                <div class="feature-item">
                    <h6><i class="fas fa-mobile-alt me-2"></i>响应式设计</h6>
                    <p>针对移动设备和触摸屏优化，支持高对比度和减少动画模式</p>
                </div>
                <div class="feature-item">
                    <h6><i class="fas fa-eye me-2"></i>智能隐藏</h6>
                    <p>用户不活跃时自动隐藏控制栏，悬停或操作时平滑显示</p>
                </div>
                <div class="feature-item">
                    <h6><i class="fas fa-magic me-2"></i>悬停效果</h6>
                    <p>所有按钮都有精美的悬停动画，提供即时的视觉反馈</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h4>🎯 测试要点</h4>
            <ul>
                <li>✅ <strong>渐变背景</strong>：控制栏使用透明到黑色的渐变背景</li>
                <li>✅ <strong>毛玻璃效果</strong>：backdrop-filter模糊效果</li>
                <li>✅ <strong>圆形按钮</strong>：播放、音量、全屏按钮为圆形设计</li>
                <li>✅ <strong>彩色进度条</strong>：红色渐变的播放进度</li>
                <li>✅ <strong>悬停动画</strong>：按钮悬停时的缩放和颜色变化</li>
                <li>✅ <strong>智能隐藏</strong>：不活跃时自动隐藏控制栏</li>
                <li>✅ <strong>倍速显示</strong>：倍速按钮采用胶囊形状设计</li>
                <li>✅ <strong>时间显示</strong>：清晰的时间文字，带有阴影效果</li>
            </ul>
        </div>

        <div class="test-section">
            <h4>🎮 操作指南</h4>
            <ol>
                <li><strong>播放控制</strong>：点击圆形播放按钮开始/暂停播放</li>
                <li><strong>进度控制</strong>：拖拽进度条或点击任意位置跳转</li>
                <li><strong>音量控制</strong>：点击音量按钮静音，拖拽音量滑块调节</li>
                <li><strong>倍速播放</strong>：点击倍速按钮选择播放速度</li>
                <li><strong>全屏模式</strong>：点击全屏按钮进入/退出全屏</li>
                <li><strong>自动隐藏</strong>：停止操作3秒后控制栏自动隐藏</li>
            </ol>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Video.js JavaScript -->
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');

            // 初始化Video.js播放器
            const player = videojs('video-player', {
                fluid: false,
                responsive: false,
                width: 270,
                height: 380,
                playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
                controls: true,
                preload: 'metadata'
            });

            // 播放事件监听
            player.ready(function() {
                console.log('现代化控制栏播放器已准备就绪');

                player.on('play', function() {
                    console.log('视频开始播放');
                });

                player.on('pause', function() {
                    console.log('视频已暂停');
                });

                player.on('ended', function() {
                    console.log('视频播放结束');
                });

                player.on('error', function(e) {
                    console.error('视频播放出错:', e);
                });
            });

            // 设置视频源
            player.src({
                type: 'video/mp4',
                src: 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/jyqk-sos-01.mp4'
            });
        });
    </script>
</body>
</html>
