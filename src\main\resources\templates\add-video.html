<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle}">添加视频</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/add-video-style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 返回按钮 -->
                <div>
                    <a href="/admin" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>返回管理
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container my-4">
        <!-- 页面头部 -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/admin">视频管理</a></li>
                        <li class="breadcrumb-item active">添加视频</li>
                    </ol>
                </nav>
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-plus me-3"></i>添加视频
                </h1>
                <p class="lead">填写视频信息，添加新的视频内容。</p>
            </div>
        </div>

        <!-- 添加视频表单 -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>视频信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="addVideoForm" th:object="${video}" onsubmit="submitAddVideo(event)">
                            <!-- 基本信息 -->
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-heading me-1"></i>视频标题 <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           th:field="*{title}" required maxlength="200" 
                                           placeholder="请输入视频标题（最多200个字符）">
                                    <div class="form-text">建议使用简洁明了的标题，便于用户搜索和识别。</div>
                                </div>
                            </div>

                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label for="videoUrl" class="form-label">
                                        <i class="fas fa-link me-1"></i>视频链接 <span class="text-danger">*</span>
                                    </label>
                                    <input type="url" class="form-control" id="videoUrl" name="videoUrl" 
                                           th:field="*{videoUrl}" required 
                                           placeholder="https://your-bucket.oss-cn-hangzhou.aliyuncs.com/videos/sample.mp4">
                                    <div class="form-text">支持阿里云OSS、腾讯云COS等云存储视频链接。</div>
                                </div>
                            </div>

                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label for="thumbnailUrl" class="form-label">
                                        <i class="fas fa-image me-1"></i>缩略图链接
                                    </label>
                                    <input type="url" class="form-control" id="thumbnailUrl" name="thumbnailUrl" 
                                           th:field="*{thumbnailUrl}" 
                                           placeholder="https://your-bucket.oss-cn-hangzhou.aliyuncs.com/thumbnails/sample.jpg">
                                    <div class="form-text">建议使用16:9比例的图片作为缩略图，提升视觉效果。</div>
                                </div>
                            </div>

                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label for="description" class="form-label">
                                        <i class="fas fa-align-left me-1"></i>视频描述
                                    </label>
                                    <textarea class="form-control" id="description" name="description" 
                                              th:field="*{description}" rows="4" maxlength="500"
                                              placeholder="请输入视频描述（最多500个字符）"></textarea>
                                    <div class="form-text">详细描述视频内容，帮助用户了解视频主题。</div>
                                </div>
                            </div>

                            <!-- 技术信息 -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-4">
                                    <label for="videoFormat" class="form-label">
                                        <i class="fas fa-file-video me-1"></i>视频格式
                                    </label>
                                    <select class="form-select" id="videoFormat" name="videoFormat" th:field="*{videoFormat}">
                                        <option value="">请选择格式</option>
                                        <option value="mp4">MP4</option>
                                        <option value="avi">AVI</option>
                                        <option value="mov">MOV</option>
                                        <option value="wmv">WMV</option>
                                        <option value="flv">FLV</option>
                                        <option value="webm">WebM</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="resolution" class="form-label">
                                        <i class="fas fa-tv me-1"></i>分辨率
                                    </label>
                                    <select class="form-select" id="resolution" name="resolution" th:field="*{resolution}">
                                        <option value="">请选择分辨率</option>
                                        <option value="4K">4K (3840×2160)</option>
                                        <option value="1080p">1080p (1920×1080)</option>
                                        <option value="720p">720p (1280×720)</option>
                                        <option value="480p">480p (854×480)</option>
                                        <option value="360p">360p (640×360)</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="duration" class="form-label">
                                        <i class="fas fa-clock me-1"></i>时长（秒）
                                    </label>
                                    <input type="number" class="form-control" id="duration" name="duration" 
                                           th:field="*{duration}" min="1" placeholder="例如：120">
                                </div>
                            </div>

                            <!-- 预览区域 -->
                            <div class="row g-3 mb-4 preview-section" id="previewSection">
                                <div class="col-12">
                                    <h6><i class="fas fa-eye me-2"></i>预览</h6>
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <img id="previewThumbnail" src="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png"
                                                         class="img-fluid rounded" alt="缩略图预览">
                                                </div>
                                                <div class="col-md-8">
                                                    <h6 id="previewTitle">视频标题</h6>
                                                    <p id="previewDescription" class="text-muted">视频描述</p>
                                                    <div class="d-flex gap-2">
                                                        <span id="previewFormat" class="badge bg-secondary">格式</span>
                                                        <span id="previewResolution" class="badge bg-info">分辨率</span>
                                                        <span id="previewDuration" class="badge bg-success">时长</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>保存视频
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="previewVideo()">
                                            <i class="fas fa-eye me-2"></i>预览
                                        </button>
                                        <a href="/admin" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>返回列表
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-play-circle me-2"></i>视频播放器</h5>
                    <p class="mb-0">专业的网页视频播放解决方案。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 佳茵轻康. <a href="/about" class="text-light text-decoration-none">关于我们</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/navbar-fix.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/admin.js"></script>
</body>
</html>
