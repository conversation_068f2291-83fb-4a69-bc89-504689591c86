package com.videoplayer.repository;

import com.videoplayer.entity.LeadContact;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 联系信息数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface LeadContactRepository extends JpaRepository<LeadContact, Long> {

    /**
     * 查找所有启用的联系信息，按排序顺序排列
     */
    @Query("SELECT lc FROM LeadContact lc WHERE lc.isActive = true ORDER BY lc.sortOrder ASC, lc.id ASC")
    List<LeadContact> findActiveContactsOrderBySortOrder();

    /**
     * 查找指定数量的启用联系信息
     */
    @Query("SELECT lc FROM LeadContact lc WHERE lc.isActive = true ORDER BY lc.sortOrder ASC, lc.id ASC")
    List<LeadContact> findActiveContactsWithLimit(@Param("limit") int limit);

    /**
     * 查找默认联系信息
     */
    @Query("SELECT lc FROM LeadContact lc WHERE lc.isActive = true AND lc.isDefault = true ORDER BY lc.sortOrder ASC")
    Optional<LeadContact> findDefaultContact();

    /**
     * 查找所有默认联系信息（可能有多个）
     */
    @Query("SELECT lc FROM LeadContact lc WHERE lc.isActive = true AND lc.isDefault = true ORDER BY lc.sortOrder ASC")
    List<LeadContact> findAllDefaultContacts();

    /**
     * 根据联系人姓名查找
     */
    List<LeadContact> findByContactNameContainingAndIsActiveTrue(String contactName);

    /**
     * 根据电话号码查找
     */
    Optional<LeadContact> findByPhoneAndIsActiveTrue(String phone);

    /**
     * 根据微信号查找
     */
    Optional<LeadContact> findByWechatAndIsActiveTrue(String wechat);

    /**
     * 根据抖音号查找
     */
    Optional<LeadContact> findByDouyinAndIsActiveTrue(String douyin);

    /**
     * 根据联系类型查找
     */
    List<LeadContact> findByContactTypeAndIsActiveTrue(String contactType);

    /**
     * 统计启用的联系信息数量
     */
    @Query("SELECT COUNT(lc) FROM LeadContact lc WHERE lc.isActive = true")
    long countActiveContacts();

    /**
     * 查找最大排序顺序
     */
    @Query("SELECT COALESCE(MAX(lc.sortOrder), 0) FROM LeadContact lc")
    Integer findMaxSortOrder();
}
