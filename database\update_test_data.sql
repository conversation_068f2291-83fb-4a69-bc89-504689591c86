-- 更新测试数据脚本
-- 为视频播放器添加真实的测试数据

USE `video_player`;

-- 清空现有的示例数据
DELETE FROM `videos` WHERE `title` LIKE '示例视频%' OR `title` = '测试视频';

-- 插入真实的测试视频数据
INSERT INTO `videos` (`title`, `description`, `video_url`, `thumbnail_url`, `duration`, `video_format`, `resolution`) VALUES
('佳茵轻康：瘦身产品使用指南', '详细介绍佳茵轻康瘦身产品的正确使用方法，帮助您安全有效地达到理想体重。', 
 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/guide-01.mp4', 
 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png', 
 300, 'mp4', '1080p'),

('健康饮食搭配建议', '专业营养师为您推荐的健康饮食搭配方案，配合佳茵轻康产品效果更佳。', 
 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/diet-guide.mp4', 
 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png', 
 420, 'mp4', '1080p'),

('运动健身基础教程', '简单易学的居家运动教程，无需器械，随时随地开始您的健康之旅。', 
 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/exercise-basic.mp4', 
 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png', 
 600, 'mp4', '720p'),

('成功案例分享', '真实用户分享使用佳茵轻康产品的成功经验和心得体会。', 
 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/success-story.mp4', 
 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png', 
 480, 'mp4', '1080p'),

('产品介绍与特色', '全面介绍佳茵轻康产品的特色功能和独特优势。', 
 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/product-intro.mp4', 
 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png', 
 360, 'mp4', '1080p');

-- 确保联系信息数据存在
INSERT IGNORE INTO `lead_contacts` (`contact_name`, `phone`, `wechat`, `douyin`, `douyin_nickname`, `contact_type`, `is_default`, `sort_order`) VALUES
('林佳', '18722880704', '18722880704', '黄林佳', '皮皮管理', '主管', TRUE, 1),
('黄超', '18057722960', '18057722960', '黄超(黄小燕弟弟)', '黄超', '主管', FALSE, 2),
('小班', '15908542510', '15908542510', '佳茵轻康SOS小班', '小班', '客服', FALSE, 3);

-- 显示更新结果
SELECT '测试数据更新完成！' as message;
SELECT COUNT(*) as video_count FROM videos WHERE is_active = TRUE;
SELECT COUNT(*) as contact_count FROM lead_contacts WHERE is_active = TRUE;
