@echo off
chcp 65001 >nul
title 视频播放器 - Video Player

echo ==========================================
echo 视频播放器启动脚本
echo Video Player Startup Script
echo ==========================================

REM 检查Java环境
echo 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请先安装JDK 17或更高版本
    echo Error: Java not found, please install JDK 17 or higher
    pause
    exit /b 1
)

REM 获取Java版本
for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%g
)
set JAVA_VERSION=%JAVA_VERSION:"=%
for /f "delims=. tokens=1-3" %%v in ("%JAVA_VERSION%") do (
    set JAVA_MAJOR=%%v
)

if %JAVA_MAJOR% lss 17 (
    echo 错误: Java版本过低，需要JDK 17或更高版本
    echo Error: Java version too low, JDK 17 or higher required
    pause
    exit /b 1
)

echo Java版本检查通过
java -version

REM 检查JAR文件
set JAR_FILE=target\video-player-1.0.0.jar
if not exist "%JAR_FILE%" (
    echo 错误: 未找到JAR文件，请先运行 mvn clean package
    echo Error: JAR file not found, please run 'mvn clean package' first
    pause
    exit /b 1
)

echo 找到JAR文件: %JAR_FILE%

REM 设置JVM参数
set JVM_OPTS=-Xms512m -Xmx2g -server
set JVM_OPTS=%JVM_OPTS% -Djava.awt.headless=true
set JVM_OPTS=%JVM_OPTS% -Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Duser.timezone=Asia/Shanghai

REM 设置Spring配置文件
set SPRING_OPTS=--spring.profiles.active=prod

REM 检查端口占用
set PORT=8080
netstat -an | findstr ":%PORT% " >nul 2>&1
if %errorlevel% equ 0 (
    echo 警告: 端口 %PORT% 已被占用，请检查是否有其他应用在运行
    echo Warning: Port %PORT% is already in use
    set /p CONTINUE="是否继续启动? (y/N): "
    if /i not "%CONTINUE%"=="y" (
        exit /b 1
    )
)

REM 创建日志目录
if not exist "logs" mkdir logs

echo ==========================================
echo 启动参数:
echo JVM Options: %JVM_OPTS%
echo Spring Options: %SPRING_OPTS%
echo Port: %PORT%
echo ==========================================

REM 启动应用
echo 正在启动视频播放器...
echo Starting Video Player...
echo.
echo 启动成功后请访问: http://localhost:%PORT%
echo After startup, please visit: http://localhost:%PORT%
echo.
echo 按 Ctrl+C 停止应用
echo Press Ctrl+C to stop the application
echo ==========================================

java %JVM_OPTS% -jar %JAR_FILE% %SPRING_OPTS%

echo.
echo 应用已退出
echo Application exited
pause

