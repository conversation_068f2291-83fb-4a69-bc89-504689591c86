/**
 * 简化的缩略图优化脚本
 * 修复显示bug，提供基本的加载优化
 */

(function() {
    'use strict';

    const DEFAULT_IMAGE = 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png';

    /**
     * 初始化缩略图
     */
    function initThumbnails() {
        const images = document.querySelectorAll('.thumbnail-optimized');
        
        images.forEach(img => {
            setupImage(img);
        });
    }

    /**
     * 设置单个图片
     */
    function setupImage(img) {
        // 如果图片已经加载完成
        if (img.complete && img.naturalHeight !== 0) {
            img.classList.add('loaded');
            img.style.opacity = '1';
            return;
        }

        // 设置加载事件
        img.addEventListener('load', function() {
            this.classList.add('loaded');
            this.style.opacity = '1';
            this.dataset.loaded = 'true';
        }, { once: true });

        // 设置错误事件
        img.addEventListener('error', function() {
            if (this.src !== DEFAULT_IMAGE) {
                this.src = DEFAULT_IMAGE;
                this.classList.add('loaded', 'error');
                this.style.opacity = '1';
                this.dataset.loaded = 'true';
            }
        }, { once: true });

        // 如果图片还没开始加载，设置初始透明度
        if (!img.classList.contains('loaded')) {
            img.style.opacity = '0.7';
        }
    }

    /**
     * 懒加载支持
     */
    function setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        setupImage(img);
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '100px 0px',
                threshold: 0.1
            });

            // 观察所有懒加载图片
            const lazyImages = document.querySelectorAll('.thumbnail-optimized[loading="lazy"]');
            lazyImages.forEach(img => {
                observer.observe(img);
            });
        }
    }

    /**
     * 处理动态添加的图片
     */
    function observeNewImages() {
        if ('MutationObserver' in window) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) { // Element node
                                const newImages = node.querySelectorAll ? 
                                    node.querySelectorAll('.thumbnail-optimized') : [];
                                newImages.forEach(setupImage);
                                
                                // 如果节点本身是图片
                                if (node.classList && node.classList.contains('thumbnail-optimized')) {
                                    setupImage(node);
                                }
                            }
                        });
                    }
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    /**
     * 预加载默认图片
     */
    function preloadDefaultImage() {
        const img = new Image();
        img.src = DEFAULT_IMAGE;
    }

    /**
     * 页面加载完成后初始化
     */
    function init() {
        preloadDefaultImage();
        initThumbnails();
        setupLazyLoading();
        observeNewImages();
    }

    // 根据页面状态初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 导出到全局（用于调试）
    window.thumbnailSimple = {
        init: init,
        setupImage: setupImage,
        DEFAULT_IMAGE: DEFAULT_IMAGE
    };

})();
