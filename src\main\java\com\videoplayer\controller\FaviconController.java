package com.videoplayer.controller;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * Favicon控制器
 * 处理favicon.ico请求，避免404错误
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Controller
public class FaviconController {

    /**
     * 处理favicon.ico请求
     */
    @GetMapping("/favicon.ico")
    public ResponseEntity<Resource> favicon() {
        try {
            // 尝试从static目录加载favicon.ico
            Resource resource = new ClassPathResource("static/favicon.ico");
            
            if (resource.exists()) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.valueOf("image/x-icon"));
                headers.setCacheControl("public, max-age=31536000"); // 缓存1年
                
                return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
            } else {
                // 如果favicon.ico不存在，尝试使用images目录下的
                resource = new ClassPathResource("static/images/favicon.ico");
                
                if (resource.exists()) {
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.valueOf("image/x-icon"));
                    headers.setCacheControl("public, max-age=31536000");
                    
                    return ResponseEntity.ok()
                        .headers(headers)
                        .body(resource);
                }
            }
            
            // 如果都不存在，返回204 No Content（而不是404）
            return ResponseEntity.noContent().build();
            
        } catch (Exception e) {
            // 发生错误时返回204 No Content
            return ResponseEntity.noContent().build();
        }
    }
    
    /**
     * 处理apple-touch-icon请求（移动端）
     */
    @GetMapping({"/apple-touch-icon.png", "/apple-touch-icon-precomposed.png"})
    public ResponseEntity<Resource> appleTouchIcon() {
        try {
            // 尝试使用favicon作为apple-touch-icon
            Resource resource = new ClassPathResource("static/images/favicon01.png");
            
            if (resource.exists()) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.IMAGE_PNG);
                headers.setCacheControl("public, max-age=31536000");
                
                return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
            }
            
            return ResponseEntity.noContent().build();
            
        } catch (Exception e) {
            return ResponseEntity.noContent().build();
        }
    }
}
