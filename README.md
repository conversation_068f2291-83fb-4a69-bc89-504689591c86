# 视频播放器项目

一个基于Java Spring Boot的专业网页视频播放器，支持阿里云OSS视频播放，提供高清无损的视频播放体验。

## 项目特性

### 🎥 核心功能
- **高清视频播放**: 支持阿里云OSS存储的视频文件，保证原画质播放
- **多格式支持**: 支持MP4、AVI、MOV、WMV、FLV、WebM等主流视频格式
- **响应式设计**: 完美适配桌面端和移动端设备
- **视频管理**: 完整的视频CRUD操作，支持批量管理
- **搜索功能**: 支持视频标题和描述的全文搜索

### 🛠 技术栈
- **后端框架**: Spring Boot 3.2.0
- **数据库**: MySQL 8.0+
- **前端技术**: HTML5 + CSS3 + JavaScript + Bootstrap 5
- **视频播放器**: Video.js 8.6.1
- **Java版本**: JDK 17
- **构建工具**: Maven 3.6+

### 🎨 界面特色
- 现代化的Material Design风格界面
- 流畅的动画效果和交互体验
- 专业的视频播放控制界面
- 直观的管理后台界面

## 快速开始

### 环境要求

确保您的系统已安装以下软件：

- **JDK 17** 或更高版本
- **Maven 3.6** 或更高版本  
- **MySQL 8.0** 或更高版本
- **Git** (可选，用于版本控制)

### 安装步骤

#### 1. 数据库配置

首先创建MySQL数据库并导入初始化脚本：

```bash
# 登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE video_player CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 退出MySQL
exit

# 导入数据库结构
mysql -u root -p video_player < database/init.sql
```

#### 2. 配置数据库连接

编辑 `src/main/resources/application.yml` 文件，修改数据库连接信息：

```yaml
spring:
  datasource:
    url: ******************************************************************************************************************************************************
    username: your_username  # 修改为您的数据库用户名
    password: your_password  # 修改为您的数据库密码
```

#### 3. 编译和运行

```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

#### 4. 访问应用

项目启动成功后，在浏览器中访问：

- **首页**: http://localhost:5000
- **管理页面**: http://localhost:5000/admin
- **API接口**: http://localhost:5000/api/videos

## 使用指南

### 添加视频

1. 访问管理页面：http://localhost:5000/admin
2. 点击"添加视频"按钮
3. 填写视频信息：
   - **视频标题**: 必填，最多200个字符
   - **视频链接**: 必填，支持阿里云OSS链接
   - **缩略图链接**: 可选，建议提供以获得更好的展示效果
   - **视频描述**: 可选，最多500个字符

### 阿里云OSS配置

如果您使用阿里云OSS存储视频，请确保：

1. **访问权限**: 视频文件设置为公共读权限
2. **CORS配置**: 在OSS控制台配置跨域访问规则
3. **链接格式**: 使用完整的OSS访问链接

示例链接格式：
```
https://your-bucket.oss-cn-hangzhou.aliyuncs.com/videos/sample.mp4
```

### API接口

项目提供完整的REST API接口：

#### 视频管理接口

- `GET /api/videos` - 获取视频列表（支持分页）
- `GET /api/videos/{id}` - 获取视频详情
- `POST /api/videos` - 添加新视频
- `PUT /api/videos/{id}` - 更新视频信息
- `DELETE /api/videos/{id}` - 删除视频
- `POST /api/videos/{id}/play` - 播放视频

#### 搜索接口

- `GET /api/videos/search?keyword={keyword}` - 搜索视频
- `GET /api/videos/popular` - 获取最新视频

## 项目结构

```
video-player-project/
├── src/
│   ├── main/
│   │   ├── java/com/videoplayer/
│   │   │   ├── VideoPlayerApplication.java    # 主启动类
│   │   │   ├── controller/                    # 控制器层
│   │   │   │   ├── VideoController.java       # 视频API控制器
│   │   │   │   └── PageController.java        # 页面控制器
│   │   │   ├── service/                       # 服务层
│   │   │   │   └── VideoService.java          # 视频业务逻辑
│   │   │   ├── repository/                    # 数据访问层
│   │   │   │   └── VideoRepository.java       # 视频数据访问接口
│   │   │   ├── entity/                        # 实体类
│   │   │   │   └── Video.java                 # 视频实体
│   │   │   └── config/                        # 配置类
│   │   │       └── WebConfig.java             # Web配置
│   │   ├── resources/
│   │   │   ├── application.yml                # 应用配置
│   │   │   ├── static/                        # 静态资源
│   │   │   │   ├── css/style.css              # 样式文件
│   │   │   │   ├── js/main.js                 # JavaScript文件
│   │   │   │   └── images/                    # 图片资源
│   │   │   └── templates/                     # 页面模板
│   │   │       ├── index.html                 # 首页
│   │   │       ├── play.html                  # 播放页面
│   │   │       ├── admin.html                 # 管理页面
│   │   │       └── error.html                 # 错误页面
│   │   └── webapp/                            # Web应用目录
│   └── test/                                  # 测试代码
├── database/
│   └── init.sql                               # 数据库初始化脚本
├── pom.xml                                    # Maven配置文件
└── README.md                                  # 项目说明文档
```

## 部署指南

### 生产环境部署

#### 1. 打包应用

```bash
# 打包为可执行JAR文件
mvn clean package -DskipTests

# 生成的JAR文件位于 target/ 目录下
ls target/*.jar
```

#### 2. 配置生产环境

创建生产环境配置文件 `application-prod.yml`：

```yaml
server:
  port: 8080

spring:
  profiles:
    active: prod
  datasource:
    url: ************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    com.videoplayer: INFO
    org.springframework.web: WARN
```

#### 3. 运行应用

```bash
# 使用生产配置运行
java -jar target/video-player-1.0.0.jar --spring.profiles.active=prod

# 或者使用环境变量
export DB_USERNAME=your_username
export DB_PASSWORD=your_password
java -jar target/video-player-1.0.0.jar --spring.profiles.active=prod
```

### Docker部署（可选）

创建 `Dockerfile`：

```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/video-player-1.0.0.jar app.jar

EXPOSE 8080

CMD ["java", "-jar", "app.jar"]
```

构建和运行Docker容器：

```bash
# 构建镜像
docker build -t video-player .

# 运行容器
docker run -p 8080:8080 -e DB_USERNAME=your_username -e DB_PASSWORD=your_password video-player
```

## 常见问题

### Q: 视频无法播放怎么办？

A: 请检查以下几点：
1. 确认视频链接是否可以直接访问
2. 检查视频格式是否受支持
3. 确认阿里云OSS的CORS配置是否正确
4. 查看浏览器控制台是否有错误信息

### Q: 如何配置阿里云OSS的CORS？

A: 在阿里云OSS控制台中：
1. 进入Bucket管理页面
2. 选择"权限管理" > "跨域设置"
3. 添加跨域规则：
   - 来源：*
   - 允许Methods：GET, POST, PUT, DELETE, HEAD
   - 允许Headers：*

### Q: 数据库连接失败怎么办？

A: 请检查：
1. MySQL服务是否正常运行
2. 数据库连接信息是否正确
3. 数据库用户是否有足够的权限
4. 防火墙是否阻止了数据库连接

### Q: 如何修改默认端口？

A: 在 `application.yml` 中修改：
```yaml
server:
  port: 你的端口号
```

## 技术支持

如果您在使用过程中遇到问题，可以：

1. 查看项目日志文件获取详细错误信息
2. 检查数据库连接和配置是否正确
3. 确认Java和Maven版本是否符合要求
4. 验证视频链接是否有效

## 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基本的视频播放功能
- 提供完整的视频管理界面
- 集成阿里云OSS支持
- 响应式设计适配移动端

---

**感谢使用视频播放器项目！** 🎉

