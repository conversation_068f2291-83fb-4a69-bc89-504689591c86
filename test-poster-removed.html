<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>vjs-poster完全删除测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    
    <style>
        /* Video.js播放器样式 - 完全无vjs-poster版本 */
        .video-js {
            width: 270px !important;
            height: 380px !important;
            background-color: #000;
            background-image: url('https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            max-width: none !important;
            max-height: none !important;
        }

        /* vjs-tech 层特定尺寸设置 */
        .video-js .vjs-tech {
            width: 270px !important;
            height: 380px !important;
            object-fit: cover;
        }

        /* 确保所有Video.js子元素都适配固定尺寸 */
        .video-js video {
            width: 270px !important;
            height: 380px !important;
            object-fit: cover;
        }

        /* 禁用Video.js的fluid模式对尺寸的影响 */
        .video-js.vjs-fluid {
            width: 270px !important;
            height: 380px !important;
            padding-top: 0 !important;
        }

        /* 确保Video.js容器不会被responsive设置影响 */
        .video-js.vjs-responsive {
            width: 270px !important;
            height: 380px !important;
            max-width: 270px !important;
            max-height: 380px !important;
        }

        /* 大播放按钮样式 */
        .video-js .vjs-big-play-button {
            background-color: rgba(0, 0, 0, 0.45);
            border: 2px solid #fff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            line-height: 60px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            transition: all 0.3s ease;
        }

        /* 大播放按钮悬停效果 */
        .video-js:hover .vjs-big-play-button {
            background-color: rgba(0, 0, 0, 0.65);
            transform: translate(-50%, -50%) scale(1.1);
        }

        /* 控制栏样式 */
        .video-js .vjs-control-bar {
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        /* 播放器容器 */
        .video-player-container {
            position: relative;
            background: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            width: 100%;
            max-width: 310px;
            margin-left: auto;
            margin-right: auto;
            padding: 20px;
        }

        .video-wrapper {
            position: relative;
            width: 270px;
            height: 380px;
            background: #000;
            margin: 0 auto;
            border-radius: 8px;
            overflow: hidden;
        }

        .video-wrapper .video-js {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        body {
            background: #f8f9fa;
            padding: 20px 0;
        }

        .comparison {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .status-indicator {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }

        .status-ready { background: #d4edda; color: #155724; }
        .status-playing { background: #d1ecf1; color: #0c5460; }
        .status-paused { background: #fff3cd; color: #856404; }
        .status-ended { background: #f8d7da; color: #721c24; }

        .poster-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">vjs-poster完全删除测试</h1>
        
        <div class="comparison">
            <h3 class="text-center mb-3">删除vjs-poster后的播放器</h3>
            <p class="text-center text-muted mb-4">使用CSS背景图片完全替代vjs-poster功能</p>
            
            <!-- 状态指示器 -->
            <div id="status-indicator" class="status-indicator status-ready">
                <i class="fas fa-circle me-2"></i>播放器已准备就绪
            </div>

            <!-- poster信息 -->
            <div class="poster-info">
                <strong>poster功能状态：</strong>
                <div id="poster-info">检测中...</div>
            </div>
            
            <!-- 视频播放器容器 -->
            <div class="video-player-container">
                <div class="video-wrapper">
                    <!-- Video.js 播放器 - 完全无poster属性 -->
                    <video
                        id="video-player"
                        class="video-js vjs-default-skin vjs-big-play-centered"
                        controls
                        preload="metadata"
                        width="270"
                        height="380"
                        data-setup='{
                            "fluid": false,
                            "responsive": false,
                            "controls": true,
                            "autoplay": false,
                            "preload": "metadata",
                            "playbackRates": [0.5, 0.75, 1, 1.25, 1.5, 2]
                        }'
                        data-thumbnail-url="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png">
                        <source src="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/jyqk-sos-01.mp4" type="video/mp4">
                        <p class="vjs-no-js">
                            要播放此视频，请启用JavaScript，并考虑升级到
                            <a href="https://videojs.com/html5-video-support/" target="_blank">
                                支持HTML5视频的Web浏览器
                            </a>。
                        </p>
                    </video>
                </div>
            </div>
        </div>

        <div class="comparison">
            <h4>删除确认：</h4>
            <ul>
                <li>❌ <strong>poster HTML属性</strong>：完全删除，不再有poster=""</li>
                <li>❌ <strong>vjs-poster CSS样式</strong>：完全删除.video-js .vjs-poster选择器</li>
                <li>❌ <strong>移动端vjs-poster引用</strong>：删除所有媒体查询中的引用</li>
                <li>✅ <strong>CSS背景图片替代</strong>：使用background-image实现相同效果</li>
                <li>✅ <strong>JavaScript动态控制</strong>：播放/暂停/结束时的背景切换</li>
            </ul>
        </div>

        <div class="comparison">
            <h4>功能验证：</h4>
            <ol>
                <li><strong>初始状态</strong>：应该显示缩略图背景（CSS background-image）</li>
                <li><strong>点击播放</strong>：背景图片应该消失，显示视频内容</li>
                <li><strong>暂停视频</strong>：背景图片应该重新出现</li>
                <li><strong>继续播放</strong>：背景图片应该再次消失</li>
                <li><strong>视频结束</strong>：背景图片应该重新出现</li>
                <li><strong>观看统计</strong>：播放时应该记录观看次数</li>
            </ol>
        </div>

        <div class="comparison">
            <h4>技术实现：</h4>
            <ul>
                <li><strong>完全删除</strong>：poster属性、vjs-poster CSS样式</li>
                <li><strong>CSS替代</strong>：background-image + background-size: cover</li>
                <li><strong>JavaScript增强</strong>：动态背景图片管理</li>
                <li><strong>事件监听</strong>：play、pause、ended事件处理</li>
                <li><strong>功能保持</strong>：所有原有功能完全正常</li>
            </ul>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Video.js JavaScript -->
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>

    <script>
        // 初始化视频播放器
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const thumbnailUrl = videoElement.dataset.thumbnailUrl;
            const statusIndicator = document.getElementById('status-indicator');
            const posterInfo = document.getElementById('poster-info');

            // 检查poster属性是否存在
            const hasPosterAttr = videoElement.hasAttribute('poster');
            const posterAttrValue = videoElement.getAttribute('poster');
            
            posterInfo.innerHTML = `
                <div>❌ poster HTML属性：${hasPosterAttr ? '存在' : '不存在'} ${hasPosterAttr ? '⚠️' : '✅'}</div>
                <div>✅ data-thumbnail-url：${videoElement.dataset.thumbnailUrl ? '存在' : '不存在'}</div>
                <div>✅ CSS背景图片：${getComputedStyle(videoElement).backgroundImage !== 'none' ? '已设置' : '未设置'}</div>
                <div>✅ 替代方案：JavaScript动态控制</div>
            `;

            // 设置视频播放器背景图片（替代poster功能）
            if (thumbnailUrl) {
                videoElement.style.backgroundImage = `url('${thumbnailUrl}')`;
            } else {
                videoElement.style.backgroundImage = "url('https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png')";
            }

            // 初始化Video.js播放器
            const player = videojs('video-player', {
                fluid: false,
                responsive: false,
                width: 270,
                height: 380,
                playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
                controls: true,
                preload: 'metadata'
            });

            // 播放事件监听
            player.ready(function() {
                console.log('无vjs-poster播放器已准备就绪');

                // 播放开始时隐藏背景图片（替代poster隐藏功能）
                player.on('play', function() {
                    videoElement.style.backgroundImage = 'none';
                    statusIndicator.className = 'status-indicator status-playing';
                    statusIndicator.innerHTML = '<i class="fas fa-play me-2"></i>正在播放 - CSS背景已隐藏';
                    console.log('播放开始 - CSS背景图片已隐藏');
                    
                    // 模拟观看次数记录
                    console.log('记录观看次数...');
                });

                // 视频暂停时恢复背景图片
                player.on('pause', function() {
                    if (thumbnailUrl) {
                        videoElement.style.backgroundImage = `url('${thumbnailUrl}')`;
                    } else {
                        videoElement.style.backgroundImage = "url('https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png')";
                    }
                    statusIndicator.className = 'status-indicator status-paused';
                    statusIndicator.innerHTML = '<i class="fas fa-pause me-2"></i>已暂停 - CSS背景已恢复';
                    console.log('视频暂停 - CSS背景图片已恢复');
                });

                // 视频结束时恢复背景图片
                player.on('ended', function() {
                    if (thumbnailUrl) {
                        videoElement.style.backgroundImage = `url('${thumbnailUrl}')`;
                    } else {
                        videoElement.style.backgroundImage = "url('https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png')";
                    }
                    statusIndicator.className = 'status-indicator status-ended';
                    statusIndicator.innerHTML = '<i class="fas fa-stop me-2"></i>播放结束 - CSS背景已恢复';
                    console.log('视频结束 - CSS背景图片已恢复');
                });

                // 错误处理
                player.on('error', function(e) {
                    console.error('视频播放出错:', e);
                    statusIndicator.className = 'status-indicator status-ended';
                    statusIndicator.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>播放出错';
                });
            });

            // 设置视频源
            player.src({
                type: 'video/mp4',
                src: 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/jyqk-sos-01.mp4'
            });
        });
    </script>
</body>
</html>
