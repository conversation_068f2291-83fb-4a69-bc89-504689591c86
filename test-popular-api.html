<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试Popular API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Popular Videos API 测试</h1>
    
    <div>
        <button onclick="testPopularAPI()">测试 /api/videos/popular</button>
        <button onclick="testPopularAPIWithLimit()">测试 /api/videos/popular?limit=3</button>
        <button onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(title, content, type = 'loading') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div>${content}</div>
            `;
            resultsDiv.appendChild(resultDiv);
            return resultDiv;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testPopularAPI() {
            const resultDiv = addResult('测试 /api/videos/popular', '正在请求...', 'loading');
            
            try {
                const response = await fetch('/api/videos/popular?limit=5');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        <h3>✅ 测试成功 - /api/videos/popular</h3>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>成功:</strong> ${data.success}</p>
                        <p><strong>视频数量:</strong> ${data.videos ? data.videos.length : 0}</p>
                        <p><strong>响应数据:</strong></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        <h3>❌ 测试失败 - /api/videos/popular</h3>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>错误信息:</strong> ${data.message || '未知错误'}</p>
                        <p><strong>响应数据:</strong></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <h3>❌ 网络错误 - /api/videos/popular</h3>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <p>可能的原因：</p>
                    <ul>
                        <li>服务器未启动</li>
                        <li>数据库连接问题</li>
                        <li>网络连接问题</li>
                    </ul>
                `;
            }
        }

        async function testPopularAPIWithLimit() {
            const resultDiv = addResult('测试 /api/videos/popular?limit=3', '正在请求...', 'loading');
            
            try {
                const response = await fetch('/api/videos/popular?limit=3');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        <h3>✅ 测试成功 - /api/videos/popular?limit=3</h3>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>成功:</strong> ${data.success}</p>
                        <p><strong>视频数量:</strong> ${data.videos ? data.videos.length : 0}</p>
                        <p><strong>限制参数:</strong> 3</p>
                        <p><strong>实际返回:</strong> ${data.videos ? data.videos.length : 0} 个视频</p>
                        <p><strong>响应数据:</strong></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        <h3>❌ 测试失败 - /api/videos/popular?limit=3</h3>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>错误信息:</strong> ${data.message || '未知错误'}</p>
                        <p><strong>响应数据:</strong></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <h3>❌ 网络错误 - /api/videos/popular?limit=3</h3>
                    <p><strong>错误:</strong> ${error.message}</p>
                `;
            }
        }

        // 页面加载时自动测试
        window.addEventListener('load', function() {
            addResult('页面加载完成', '准备测试API接口', 'success');
        });
    </script>
</body>
</html>
