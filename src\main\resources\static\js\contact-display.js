/**
 * 简化的联系信息显示组件
 * 替代原有的3D轮播组件，提供简单的联系信息显示功能
 * <AUTHOR>
 * @version 1.0.0
 */
class ContactDisplay {
    constructor() {
        this.contactData = [];
        this.currentIndex = 0;
        this.defaultIndex = 0;
        this.isInitialized = false;
        
        // 默认联系信息
        this.defaultContactData = [
            {
                id: 1,
                contactName: '林佳',
                phone: '18722880704',
                wechat: '18722880704',
                douyin: '黄林佳',
                douyinNickname: '皮皮管理',
                isDefault: true,
                sortOrder: 0
            }
        ];
        

        
        this.contactTextElement = document.getElementById('contact-text');
    }
    
    /**
     * 初始化组件
     */
    async init() {
        if (this.isInitialized) {
            return;
        }

        // 直接使用备用数据，不进行网络请求
        this.contactData = [...this.defaultContactData];

        // 检测默认项目
        this.detectDefaultItem();

        // 显示联系信息
        this.displayContact();

        // 标记为已初始化
        this.isInitialized = true;
    }
    

    
    /**
     * 检测默认项目
     */
    detectDefaultItem() {
        if (this.contactData.length === 0) return;

        // 查找标记为默认的项目
        const defaultItem = this.contactData.find(contact => contact.isDefault === true);

        if (defaultItem) {
            this.defaultIndex = this.contactData.indexOf(defaultItem);
        } else {
            // 如果没有找到默认项目，使用第一个
            this.defaultIndex = 0;
        }

        this.currentIndex = this.defaultIndex;
    }
    
    /**
     * 生成联系信息显示文本
     */
    generateDisplayText(contact) {
        if (!contact) return '';

        let firstLine = '';
        let secondLine = '';

        // 第一行：微信/电话部分
        if (contact.wechat && contact.phone) {
            firstLine = `微信/电话：${contact.phone}【${contact.contactName}】`;
        } else if (contact.phone) {
            firstLine = `电话：${contact.phone}【${contact.contactName}】`;
        } else if (contact.wechat) {
            firstLine = `微信：${contact.wechat}【${contact.contactName}】`;
        }

        // 第二行：抖音部分
        if (contact.douyin) {
            secondLine = '抖音号：';
            if (contact.douyinNickname) {
                // 特殊处理：如果抖音昵称包含皮皮管理，按指定格式显示
                if (contact.douyinNickname.includes('皮皮管理')) {
                    secondLine += `黄林佳【皮皮管理】`;
                } else {
                    secondLine += `${contact.douyin}【${contact.douyinNickname}】`;
                }
            } else {
                secondLine += contact.douyin;
            }
        }

        // 换行显示 - 使用<br>标签
        let text = firstLine;
        if (secondLine) {
            text += `<br>${secondLine}`;
        }
        return text;
    }
    
    /**
     * 显示联系信息
     */
    displayContact() {
        if (!this.contactTextElement || this.contactData.length === 0) {
            return;
        }

        const contact = this.contactData[this.currentIndex];
        const displayText = this.generateDisplayText(contact);
        this.contactTextElement.innerHTML = displayText;
    }
    

    

    
    /**
     * 获取当前联系信息
     */
    getCurrentContact() {
        return this.contactData[this.currentIndex];
    }
    
    /**
     * 销毁组件
     */
    destroy() {
        this.isInitialized = false;
        this.contactData = [];
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', async () => {
    // 检查是否已经有实例
    if (window.contactDisplay) {
        return;
    }

    // 检查是否存在联系信息显示容器
    if (document.querySelector('.lead-contact-display')) {
        window.contactDisplay = new ContactDisplay();
        // 初始化组件
        await window.contactDisplay.init();
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.contactDisplay) {
        window.contactDisplay.destroy();
    }
});
