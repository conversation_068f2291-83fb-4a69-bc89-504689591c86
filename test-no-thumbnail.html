<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无缩略图播放器测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    
    <style>
        /* Video.js播放器样式 - 无缩略图版本 */
        .video-js {
            width: 270px !important;
            height: 380px !important;
            background-color: #000;
            max-width: none !important;
            max-height: none !important;
        }

        .video-js video {
            width: 270px !important;
            height: 380px !important;
            object-fit: cover;
        }

        .video-js.vjs-fluid {
            width: 270px !important;
            height: 380px !important;
            padding-top: 0 !important;
        }

        .video-js.vjs-responsive {
            width: 270px !important;
            height: 380px !important;
            max-width: 270px !important;
            max-height: 380px !important;
        }

        .video-js .vjs-big-play-button {
            background-color: rgba(0, 0, 0, 0.45);
            border: 2px solid #fff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            line-height: 60px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            transition: all 0.3s ease;
        }

        .video-js:hover .vjs-big-play-button {
            background-color: rgba(0, 0, 0, 0.65);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .video-js .vjs-control-bar {
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .video-player-container {
            position: relative;
            background: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            width: 100%;
            max-width: 310px;
            margin-left: auto;
            margin-right: auto;
            padding: 20px;
        }

        .video-wrapper {
            position: relative;
            width: 270px;
            height: 380px;
            background: #000;
            margin: 0 auto;
            border-radius: 8px;
            overflow: hidden;
        }

        .video-wrapper .video-js {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        body {
            background: #f8f9fa;
            padding: 20px 0;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .status-indicator {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }

        .status-success { background: #d4edda; color: #155724; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        .status-warning { background: #fff3cd; color: #856404; }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }

        .log-info { background: #e7f3ff; }
        .log-success { background: #d4edda; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">无缩略图播放器测试</h1>
        
        <div class="test-section">
            <h3 class="text-center mb-3">完全无缩略图的播放器</h3>
            <p class="text-center text-muted mb-4">刷新页面时不会出现任何缩略图</p>
            
            <!-- 状态指示器 -->
            <div id="status-indicator" class="status-indicator status-info">
                <i class="fas fa-circle me-2"></i>播放器已准备就绪 - 无缩略图
            </div>
            
            <!-- 视频播放器容器 -->
            <div class="video-player-container">
                <div class="video-wrapper">
                    <video
                        id="video-player"
                        class="video-js vjs-default-skin vjs-big-play-centered"
                        controls
                        preload="metadata"
                        width="270"
                        height="380"
                        data-setup='{
                            "fluid": false,
                            "responsive": false,
                            "controls": true,
                            "autoplay": false,
                            "preload": "metadata",
                            "playbackRates": [0.5, 0.75, 1, 1.25, 1.5, 2]
                        }'>
                        <source src="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/jyqk-sos-01.mp4" type="video/mp4">
                        <p class="vjs-no-js">
                            要播放此视频，请启用JavaScript，并考虑升级到
                            <a href="https://videojs.com/html5-video-support/" target="_blank">
                                支持HTML5视频的Web浏览器
                            </a>。
                        </p>
                    </video>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h4>验证要点：</h4>
            <ul>
                <li>✅ <strong>无缩略图显示</strong>：刷新页面时不会出现任何缩略图</li>
                <li>✅ <strong>纯黑背景</strong>：播放器显示纯黑色背景</li>
                <li>✅ <strong>播放功能正常</strong>：视频可以正常播放、暂停</li>
                <li>✅ <strong>无背景切换</strong>：播放、暂停时不会有背景图片变化</li>
                <li>✅ <strong>简洁界面</strong>：专注于视频内容本身</li>
            </ul>
        </div>

        <div class="test-section">
            <h4>测试步骤：</h4>
            <ol>
                <li><strong>刷新页面</strong>：多次刷新，观察是否出现缩略图</li>
                <li><strong>播放视频</strong>：点击播放按钮，确认功能正常</li>
                <li><strong>暂停播放</strong>：暂停时不应该出现缩略图</li>
                <li><strong>结束播放</strong>：播放结束时不应该出现缩略图</li>
                <li><strong>检查背景</strong>：始终保持纯黑色背景</li>
            </ol>
        </div>

        <div class="test-section">
            <h4>实时日志：</h4>
            <div class="log-container" id="log-container">
                <div class="log-entry log-info">页面加载完成，无缩略图设置...</div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Video.js JavaScript -->
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>

    <script>
        // 日志记录函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 初始化视频播放器
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const statusIndicator = document.getElementById('status-indicator');
            const videoId = 1; // 模拟视频ID

            addLog('开始初始化无缩略图播放器', 'info');

            // 缩略图功能已完全禁用
            // 不设置任何背景图片
            addLog('✅ 缩略图功能已完全禁用', 'success');

            // 初始化Video.js播放器
            const player = videojs('video-player', {
                fluid: false,
                responsive: false,
                width: 270,
                height: 380,
                playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
                controls: true,
                preload: 'metadata'
            });

            // 播放事件监听
            player.ready(function() {
                addLog('Video.js播放器初始化完成', 'success');

                // 播放开始事件（无缩略图处理）
                player.on('play', function() {
                    statusIndicator.className = 'status-indicator status-success';
                    statusIndicator.innerHTML = '<i class="fas fa-play me-2"></i>正在播放 - 无缩略图';
                    
                    console.log('视频开始播放，ID:', videoId);
                    addLog(`视频开始播放，ID: ${videoId}`, 'success');
                    addLog('✅ 无缩略图处理', 'success');
                });

                // 视频暂停事件（无缩略图处理）
                player.on('pause', function() {
                    statusIndicator.className = 'status-indicator status-warning';
                    statusIndicator.innerHTML = '<i class="fas fa-pause me-2"></i>已暂停 - 无缩略图';
                    
                    console.log('视频已暂停，ID:', videoId);
                    addLog(`视频已暂停，ID: ${videoId}`, 'info');
                    addLog('✅ 暂停时无缩略图显示', 'success');
                });

                // 视频结束事件（无缩略图处理）
                player.on('ended', function() {
                    statusIndicator.className = 'status-indicator status-info';
                    statusIndicator.innerHTML = '<i class="fas fa-stop me-2"></i>播放结束 - 无缩略图';
                    
                    console.log('视频播放结束，ID:', videoId);
                    addLog(`视频播放结束，ID: ${videoId}`, 'info');
                    addLog('✅ 结束时无缩略图显示', 'success');
                });

                // 错误处理
                player.on('error', function(e) {
                    addLog(`视频播放出错: ${e}`, 'error');
                    statusIndicator.className = 'status-indicator status-error';
                    statusIndicator.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>播放出错';
                });
            });

            // 设置视频源
            player.src({
                type: 'video/mp4',
                src: 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/jyqk-sos-01.mp4'
            });

            addLog('视频源设置完成', 'info');
        });

        // 页面加载完成
        window.addEventListener('load', function() {
            addLog('页面加载完成，播放器背景为纯黑色', 'success');
        });
    </script>
</body>
</html>
