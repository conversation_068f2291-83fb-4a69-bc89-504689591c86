<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>观看次数API删除验证</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    
    <style>
        /* Video.js播放器样式 */
        .video-js {
            width: 270px !important;
            height: 380px !important;
            background-color: #000;
            background-image: url('https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            max-width: none !important;
            max-height: none !important;
        }

        .video-js video {
            width: 270px !important;
            height: 380px !important;
            object-fit: cover;
        }

        .video-js.vjs-fluid {
            width: 270px !important;
            height: 380px !important;
            padding-top: 0 !important;
        }

        .video-js.vjs-responsive {
            width: 270px !important;
            height: 380px !important;
            max-width: 270px !important;
            max-height: 380px !important;
        }

        .video-js .vjs-big-play-button {
            background-color: rgba(0, 0, 0, 0.45);
            border: 2px solid #fff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            line-height: 60px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            transition: all 0.3s ease;
        }

        .video-js:hover .vjs-big-play-button {
            background-color: rgba(0, 0, 0, 0.65);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .video-js .vjs-control-bar {
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .video-player-container {
            position: relative;
            background: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            width: 100%;
            max-width: 310px;
            margin-left: auto;
            margin-right: auto;
            padding: 20px;
        }

        .video-wrapper {
            position: relative;
            width: 270px;
            height: 380px;
            background: #000;
            margin: 0 auto;
            border-radius: 8px;
            overflow: hidden;
        }

        .video-wrapper .video-js {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        body {
            background: #f8f9fa;
            padding: 20px 0;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .status-indicator {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }

        .status-success { background: #d4edda; color: #155724; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-error { background: #f8d7da; color: #721c24; }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }

        .log-info { background: #e7f3ff; }
        .log-success { background: #d4edda; }
        .log-error { background: #f8d7da; }

        .network-monitor {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">观看次数API删除验证</h1>
        
        <div class="test-section">
            <h3 class="text-center mb-3">修复后的播放器</h3>
            <p class="text-center text-muted mb-4">验证删除观看次数API调用后的效果</p>
            
            <!-- 状态指示器 -->
            <div id="status-indicator" class="status-indicator status-info">
                <i class="fas fa-circle me-2"></i>播放器已准备就绪
            </div>

            <!-- 网络监控 -->
            <div class="network-monitor">
                <strong>网络请求监控：</strong>
                <div id="network-status">等待播放操作...</div>
            </div>
            
            <!-- 视频播放器容器 -->
            <div class="video-player-container">
                <div class="video-wrapper">
                    <video
                        id="video-player"
                        class="video-js vjs-default-skin vjs-big-play-centered"
                        controls
                        preload="metadata"
                        width="270"
                        height="380"
                        data-setup='{
                            "fluid": false,
                            "responsive": false,
                            "controls": true,
                            "autoplay": false,
                            "preload": "metadata",
                            "playbackRates": [0.5, 0.75, 1, 1.25, 1.5, 2]
                        }'
                        data-thumbnail-url="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png">
                        <source src="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/jyqk-sos-01.mp4" type="video/mp4">
                        <p class="vjs-no-js">
                            要播放此视频，请启用JavaScript，并考虑升级到
                            <a href="https://videojs.com/html5-video-support/" target="_blank">
                                支持HTML5视频的Web浏览器
                            </a>。
                        </p>
                    </video>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h4>验证要点：</h4>
            <ul>
                <li>✅ <strong>无404错误</strong>：控制台不再显示/api/videos/1/view 404错误</li>
                <li>✅ <strong>播放功能正常</strong>：视频可以正常播放、暂停</li>
                <li>✅ <strong>背景图片切换</strong>：播放时隐藏，暂停时显示</li>
                <li>✅ <strong>日志记录</strong>：播放时输出简单的日志信息</li>
                <li>✅ <strong>网络请求减少</strong>：不再发送无用的观看次数请求</li>
            </ul>
        </div>

        <div class="test-section">
            <h4>操作步骤：</h4>
            <ol>
                <li><strong>点击播放</strong>：观察控制台是否有404错误</li>
                <li><strong>检查网络</strong>：开发者工具Network标签页，确认无/view请求</li>
                <li><strong>暂停播放</strong>：验证背景图片是否重新出现</li>
                <li><strong>查看日志</strong>：控制台应该显示播放日志而非错误</li>
            </ol>
        </div>

        <div class="test-section">
            <h4>实时日志：</h4>
            <div class="log-container" id="log-container">
                <div class="log-entry log-info">页面加载完成，等待用户操作...</div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Video.js JavaScript -->
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>

    <script>
        // 日志记录函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 网络监控
        let networkRequests = [];
        
        // 拦截fetch请求
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            addLog(`网络请求: ${url}`, 'info');
            
            if (url.includes('/view')) {
                addLog(`⚠️ 检测到/view请求: ${url}`, 'error');
                document.getElementById('network-status').innerHTML = 
                    '<span class="text-danger">❌ 检测到/view请求！修复未完成</span>';
            }
            
            return originalFetch.apply(this, args);
        };

        // 初始化视频播放器
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const thumbnailUrl = videoElement.dataset.thumbnailUrl;
            const statusIndicator = document.getElementById('status-indicator');
            const videoId = 1; // 模拟视频ID

            addLog('开始初始化视频播放器', 'info');

            // 设置视频播放器背景图片
            if (thumbnailUrl) {
                videoElement.style.backgroundImage = `url('${thumbnailUrl}')`;
                addLog('设置背景图片成功', 'success');
            }

            // 初始化Video.js播放器
            const player = videojs('video-player', {
                fluid: false,
                responsive: false,
                width: 270,
                height: 380,
                playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
                controls: true,
                preload: 'metadata'
            });

            // 播放事件监听
            player.ready(function() {
                addLog('Video.js播放器初始化完成', 'success');

                // 播放开始时隐藏背景图片（修复后的版本）
                player.on('play', function() {
                    videoElement.style.backgroundImage = 'none';
                    statusIndicator.className = 'status-indicator status-success';
                    statusIndicator.innerHTML = '<i class="fas fa-play me-2"></i>正在播放 - 无API调用';
                    
                    // 观看次数统计功能已删除 - 这是修复后的代码
                    console.log('视频开始播放，ID:', videoId);
                    addLog(`视频开始播放，ID: ${videoId}`, 'success');
                    addLog('✅ 无观看次数API调用', 'success');
                    
                    document.getElementById('network-status').innerHTML = 
                        '<span class="text-success">✅ 播放正常，无/view请求</span>';
                });

                // 视频暂停时恢复背景图片
                player.on('pause', function() {
                    if (thumbnailUrl) {
                        videoElement.style.backgroundImage = `url('${thumbnailUrl}')`;
                    }
                    statusIndicator.className = 'status-indicator status-warning';
                    statusIndicator.innerHTML = '<i class="fas fa-pause me-2"></i>已暂停 - 背景已恢复';
                    addLog('视频暂停，背景图片已恢复', 'info');
                });

                // 视频结束时恢复背景图片
                player.on('ended', function() {
                    if (thumbnailUrl) {
                        videoElement.style.backgroundImage = `url('${thumbnailUrl}')`;
                    }
                    statusIndicator.className = 'status-indicator status-info';
                    statusIndicator.innerHTML = '<i class="fas fa-stop me-2"></i>播放结束 - 背景已恢复';
                    addLog('视频播放结束，背景图片已恢复', 'info');
                });

                // 错误处理
                player.on('error', function(e) {
                    addLog(`视频播放出错: ${e}`, 'error');
                    statusIndicator.className = 'status-indicator status-error';
                    statusIndicator.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>播放出错';
                });
            });

            // 设置视频源
            player.src({
                type: 'video/mp4',
                src: 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/jyqk-sos-01.mp4'
            });

            addLog('视频源设置完成', 'info');
        });

        // 监听控制台错误
        window.addEventListener('error', function(e) {
            if (e.message.includes('404') && e.message.includes('/view')) {
                addLog('❌ 检测到404错误：/view接口', 'error');
                document.getElementById('network-status').innerHTML = 
                    '<span class="text-danger">❌ 仍有404错误！</span>';
            }
        });

        // 页面加载完成
        window.addEventListener('load', function() {
            addLog('页面加载完成，开始监控网络请求', 'success');
        });
    </script>
</body>
</html>
