<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle}">编辑视频</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/edit-video-style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 返回按钮 -->
                <div>
                    <a href="/admin" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>返回管理
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container my-4">
        <!-- 页面头部 -->
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/admin">视频管理</a></li>
                        <li class="breadcrumb-item active">编辑视频</li>
                    </ol>
                </nav>
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-edit me-3"></i>编辑视频
                </h1>
                <p class="lead">修改视频信息和设置。</p>
            </div>
        </div>

        <!-- 编辑视频表单 -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>视频信息
                        </h5>
                        <div class="d-flex gap-2">
                            <a th:href="@{/play/{id}(id=${video.id})}" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="fas fa-play me-1"></i>预览播放
                            </a>
                            <span class="badge bg-secondary">ID: <span th:text="${video.id}">1</span></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="editVideoForm" th:object="${video}" onsubmit="submitEditVideo(event)">
                            <input type="hidden" th:field="*{id}" id="videoId">
                            
                            <!-- 基本信息 -->
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-heading me-1"></i>视频标题 <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           th:field="*{title}" required maxlength="200" 
                                           placeholder="请输入视频标题（最多200个字符）">
                                    <div class="form-text">建议使用简洁明了的标题，便于用户搜索和识别。</div>
                                </div>
                            </div>

                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label for="videoUrl" class="form-label">
                                        <i class="fas fa-link me-1"></i>视频链接 <span class="text-danger">*</span>
                                    </label>
                                    <input type="url" class="form-control" id="videoUrl" name="videoUrl" 
                                           th:field="*{videoUrl}" required 
                                           placeholder="https://your-bucket.oss-cn-hangzhou.aliyuncs.com/videos/sample.mp4">
                                    <div class="form-text">支持阿里云OSS、腾讯云COS等云存储视频链接。</div>
                                </div>
                            </div>

                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label for="thumbnailUrl" class="form-label">
                                        <i class="fas fa-image me-1"></i>缩略图链接
                                    </label>
                                    <input type="url" class="form-control" id="thumbnailUrl" name="thumbnailUrl" 
                                           th:field="*{thumbnailUrl}" 
                                           placeholder="https://your-bucket.oss-cn-hangzhou.aliyuncs.com/thumbnails/sample.jpg">
                                    <div class="form-text">建议使用16:9比例的图片作为缩略图，提升视觉效果。</div>
                                </div>
                            </div>

                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label for="description" class="form-label">
                                        <i class="fas fa-align-left me-1"></i>视频描述
                                    </label>
                                    <textarea class="form-control" id="description" name="description" 
                                              th:field="*{description}" rows="4" maxlength="500"
                                              placeholder="请输入视频描述（最多500个字符）"></textarea>
                                    <div class="form-text">详细描述视频内容，帮助用户了解视频主题。</div>
                                </div>
                            </div>

                            <!-- 技术信息 -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-4">
                                    <label for="videoFormat" class="form-label">
                                        <i class="fas fa-file-video me-1"></i>视频格式
                                    </label>
                                    <select class="form-select" id="videoFormat" name="videoFormat" th:field="*{videoFormat}">
                                        <option value="">请选择格式</option>
                                        <option value="mp4">MP4</option>
                                        <option value="avi">AVI</option>
                                        <option value="mov">MOV</option>
                                        <option value="wmv">WMV</option>
                                        <option value="flv">FLV</option>
                                        <option value="webm">WebM</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="resolution" class="form-label">
                                        <i class="fas fa-tv me-1"></i>分辨率
                                    </label>
                                    <select class="form-select" id="resolution" name="resolution" th:field="*{resolution}">
                                        <option value="">请选择分辨率</option>
                                        <option value="4K">4K (3840×2160)</option>
                                        <option value="1080p">1080p (1920×1080)</option>
                                        <option value="720p">720p (1280×720)</option>
                                        <option value="480p">480p (854×480)</option>
                                        <option value="360p">360p (640×360)</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="duration" class="form-label">
                                        <i class="fas fa-clock me-1"></i>时长（秒）
                                    </label>
                                    <input type="number" class="form-control" id="duration" name="duration" 
                                           th:field="*{duration}" min="1" placeholder="例如：120">
                                </div>
                            </div>

                            <!-- 状态信息 -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <label for="isActive" class="form-label">
                                        <i class="fas fa-toggle-on me-1"></i>状态
                                    </label>
                                    <select class="form-select" id="isActive" name="isActive" th:field="*{isActive}">
                                        <option th:value="true">启用</option>
                                        <option th:value="false">禁用</option>
                                    </select>
                                    <div class="form-text">禁用后视频将不会在前台显示。</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">
                                        <i class="fas fa-info-circle me-1"></i>创建时间
                                    </label>
                                    <input type="text" class="form-control" readonly 
                                           th:value="${#temporals.format(video.createdTime, 'yyyy-MM-dd HH:mm:ss')}">
                                </div>
                            </div>

                            <!-- 当前缩略图预览 -->
                            <div class="row g-3 mb-4" th:if="${video.thumbnailUrl != null}">
                                <div class="col-12">
                                    <label class="form-label">
                                        <i class="fas fa-image me-1"></i>当前缩略图
                                    </label>
                                    <div>
                                        <img th:src="${video.thumbnailUrl}" class="img-thumbnail current-thumbnail"
                                             alt="当前缩略图">
                                    </div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>保存修改
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="previewVideo()">
                                            <i class="fas fa-eye me-2"></i>预览
                                        </button>
                                        <a href="/admin" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>返回列表
                                        </a>
                                        <button type="button" class="btn btn-outline-danger ms-auto" 
                                                onclick="deleteCurrentVideo()">
                                            <i class="fas fa-trash me-2"></i>删除视频
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-play-circle me-2"></i>视频播放器</h5>
                    <p class="mb-0">专业的网页视频播放解决方案。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 佳茵轻康. <a href="/about" class="text-light text-decoration-none">关于我们</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>确认删除
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除这个视频吗？</p>
                    <p class="text-muted small">此操作不可撤销，删除后视频将无法恢复。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-1"></i>确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/navbar-fix.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/admin.js"></script>
</body>
</html>
