package com.videoplayer.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 联系信息实体类
 * 用于存储轮播展示的联系方式信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Entity
@Table(name = "lead_contacts")
public class LeadContact {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "contact_name", length = 100)
    private String contactName;

    @Column(name = "phone", length = 20)
    private String phone;

    @Column(name = "wechat", length = 100)
    private String wechat;

    @Column(name = "douyin", length = 100)
    private String douyin;

    @Column(name = "douyin_nickname", length = 100)
    private String douyinNickname;

    @Column(name = "contact_type", length = 50)
    private String contactType;

    @Column(name = "is_default")
    private Boolean isDefault = false;

    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    // 构造函数
    public LeadContact() {
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
    }

    public LeadContact(String contactName, String phone, String wechat, String douyin, String douyinNickname) {
        this();
        this.contactName = contactName;
        this.phone = phone;
        this.wechat = wechat;
        this.douyin = douyin;
        this.douyinNickname = douyinNickname;
    }

    // JPA回调方法
    @PreUpdate
    public void preUpdate() {
        this.updatedTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getDouyin() {
        return douyin;
    }

    public void setDouyin(String douyin) {
        this.douyin = douyin;
    }

    public String getDouyinNickname() {
        return douyinNickname;
    }

    public void setDouyinNickname(String douyinNickname) {
        this.douyinNickname = douyinNickname;
    }

    public String getContactType() {
        return contactType;
    }

    public void setContactType(String contactType) {
        this.contactType = contactType;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    public String toString() {
        return "LeadContact{" +
                "id=" + id +
                ", contactName='" + contactName + '\'' +
                ", phone='" + phone + '\'' +
                ", wechat='" + wechat + '\'' +
                ", douyin='" + douyin + '\'' +
                ", douyinNickname='" + douyinNickname + '\'' +
                ", contactType='" + contactType + '\'' +
                ", isDefault=" + isDefault +
                ", sortOrder=" + sortOrder +
                ", isActive=" + isActive +
                '}';
    }
}
