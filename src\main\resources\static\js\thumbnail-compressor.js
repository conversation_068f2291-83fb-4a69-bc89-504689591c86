/**
 * 缩略图压缩工具
 * 用于在客户端压缩和优化缩略图
 */

class ThumbnailCompressor {
    constructor(options = {}) {
        this.options = {
            maxWidth: options.maxWidth || 320,
            maxHeight: options.maxHeight || 180,
            quality: options.quality || 0.8,
            format: options.format || 'image/jpeg',
            ...options
        };
    }

    /**
     * 压缩图片文件
     * @param {File} file - 图片文件
     * @returns {Promise<Blob>} 压缩后的图片Blob
     */
    async compressFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.compressDataURL(e.target.result)
                    .then(resolve)
                    .catch(reject);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * 压缩DataURL格式的图片
     * @param {string} dataURL - 图片DataURL
     * @returns {Promise<Blob>} 压缩后的图片Blob
     */
    async compressDataURL(dataURL) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                try {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    // 计算新的尺寸
                    const { width, height } = this.calculateDimensions(img.width, img.height);
                    
                    canvas.width = width;
                    canvas.height = height;

                    // 启用图像平滑
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    // 绘制压缩后的图片
                    ctx.drawImage(img, 0, 0, width, height);

                    // 转换为Blob
                    canvas.toBlob(resolve, this.options.format, this.options.quality);
                } catch (error) {
                    reject(error);
                }
            };
            img.onerror = reject;
            img.src = dataURL;
        });
    }

    /**
     * 计算压缩后的尺寸
     * @param {number} originalWidth - 原始宽度
     * @param {number} originalHeight - 原始高度
     * @returns {Object} 新的宽度和高度
     */
    calculateDimensions(originalWidth, originalHeight) {
        let { maxWidth, maxHeight } = this.options;
        
        // 如果原图尺寸已经小于最大尺寸，保持原尺寸
        if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
            return { width: originalWidth, height: originalHeight };
        }

        // 计算缩放比例
        const widthRatio = maxWidth / originalWidth;
        const heightRatio = maxHeight / originalHeight;
        const ratio = Math.min(widthRatio, heightRatio);

        return {
            width: Math.round(originalWidth * ratio),
            height: Math.round(originalHeight * ratio)
        };
    }

    /**
     * 批量压缩图片
     * @param {FileList|Array} files - 图片文件列表
     * @returns {Promise<Array>} 压缩后的图片Blob数组
     */
    async compressMultiple(files) {
        const promises = Array.from(files).map(file => this.compressFile(file));
        return Promise.all(promises);
    }

    /**
     * 获取压缩后的DataURL
     * @param {File} file - 图片文件
     * @returns {Promise<string>} 压缩后的DataURL
     */
    async getCompressedDataURL(file) {
        const blob = await this.compressFile(file);
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }

    /**
     * 估算压缩后的文件大小
     * @param {number} originalSize - 原始文件大小（字节）
     * @param {number} originalWidth - 原始宽度
     * @param {number} originalHeight - 原始高度
     * @returns {number} 估算的压缩后大小（字节）
     */
    estimateCompressedSize(originalSize, originalWidth, originalHeight) {
        const { width, height } = this.calculateDimensions(originalWidth, originalHeight);
        const pixelRatio = (width * height) / (originalWidth * originalHeight);
        const qualityFactor = this.options.quality;
        
        return Math.round(originalSize * pixelRatio * qualityFactor);
    }
}

/**
 * 创建缩略图预览
 * @param {File} file - 图片文件
 * @param {HTMLElement} container - 预览容器
 * @param {Object} options - 选项
 */
async function createThumbnailPreview(file, container, options = {}) {
    const compressor = new ThumbnailCompressor(options);
    
    try {
        // 显示加载状态
        container.innerHTML = `
            <div class="d-flex align-items-center justify-content-center" style="height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">压缩中...</span>
                </div>
            </div>
        `;

        // 压缩图片
        const compressedDataURL = await compressor.getCompressedDataURL(file);
        
        // 显示预览
        container.innerHTML = `
            <img src="${compressedDataURL}" 
                 class="img-fluid rounded thumbnail-optimized" 
                 alt="缩略图预览"
                 style="max-height: 200px; object-fit: cover;">
            <div class="mt-2">
                <small class="text-muted">
                    压缩后尺寸: ${compressor.calculateDimensions(0, 0).width}x${compressor.calculateDimensions(0, 0).height}
                </small>
            </div>
        `;

        // 优化新添加的图片
        if (window.optimizeNewThumbnails) {
            window.optimizeNewThumbnails(container);
        }

    } catch (error) {
        console.error('缩略图压缩失败:', error);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                缩略图压缩失败: ${error.message}
            </div>
        `;
    }
}

/**
 * 为文件输入框添加缩略图预览功能
 * @param {string} inputSelector - 文件输入框选择器
 * @param {string} previewSelector - 预览容器选择器
 * @param {Object} options - 压缩选项
 */
function setupThumbnailPreview(inputSelector, previewSelector, options = {}) {
    const input = document.querySelector(inputSelector);
    const preview = document.querySelector(previewSelector);
    
    if (!input || !preview) {
        console.warn('缩略图预览设置失败: 找不到指定的元素');
        return;
    }

    input.addEventListener('change', async function(e) {
        const file = e.target.files[0];
        if (file && file.type.startsWith('image/')) {
            await createThumbnailPreview(file, preview, options);
        } else {
            preview.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    请选择有效的图片文件
                </div>
            `;
        }
    });
}

// 导出到全局
window.ThumbnailCompressor = ThumbnailCompressor;
window.createThumbnailPreview = createThumbnailPreview;
window.setupThumbnailPreview = setupThumbnailPreview;

// 页面加载完成后自动设置预览功能
document.addEventListener('DOMContentLoaded', function() {
    // 自动为常见的缩略图输入框设置预览
    const commonSelectors = [
        { input: '#thumbnailFile', preview: '#thumbnailPreview' },
        { input: '#thumbnail', preview: '#previewThumbnail' },
        { input: 'input[type="file"][accept*="image"]', preview: '.thumbnail-preview' }
    ];

    commonSelectors.forEach(({ input, preview }) => {
        if (document.querySelector(input) && document.querySelector(preview)) {
            setupThumbnailPreview(input, preview, {
                maxWidth: 320,
                maxHeight: 180,
                quality: 0.85
            });
        }
    });
});
