package com.videoplayer.controller;

import com.videoplayer.entity.LeadContact;
import com.videoplayer.service.LeadContactService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * 联系信息API控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/lead-contacts")
public class LeadContactController {

    @Autowired
    private LeadContactService leadContactService;

    /**
     * 获取轮播展示的联系信息
     * @param limit 限制数量，0表示获取全部数据
     */
    @GetMapping("/carousel")
    public ResponseEntity<Map<String, Object>> getCarouselContacts(
            @RequestParam(defaultValue = "0") int limit) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 确保有默认数据
            leadContactService.initializeDefaultContacts();

            List<LeadContact> contacts = leadContactService.getCarouselContacts(limit);

            // 获取默认联系信息
            Optional<LeadContact> defaultContact = leadContactService.getDefaultContact();

            response.put("success", true);
            response.put("data", contacts);
            response.put("total", contacts.size());
            response.put("defaultContactId", defaultContact.map(LeadContact::getId).orElse(null));
            response.put("message", "获取联系信息成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("data", null);
            response.put("message", "获取联系信息失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取所有联系信息（不限制数量）
     */
    @GetMapping("/all")
    public ResponseEntity<Map<String, Object>> getAllContacts() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 确保有默认数据
            leadContactService.initializeDefaultContacts();

            // 获取所有联系信息（limit=0表示全部）
            List<LeadContact> contacts = leadContactService.getCarouselContacts(0);

            // 获取默认联系信息
            Optional<LeadContact> defaultContact = leadContactService.getDefaultContact();

            response.put("success", true);
            response.put("data", contacts);
            response.put("total", contacts.size());
            response.put("defaultContactId", defaultContact.map(LeadContact::getId).orElse(null));
            response.put("message", "获取所有联系信息成功");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("❌ Error getting all contacts: " + e.getMessage());
            e.printStackTrace();

            response.put("success", false);
            response.put("data", null);
            response.put("message", "获取联系信息失败: " + e.getMessage());
            response.put("total", 0);

            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 根据ID获取联系信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getContactById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            var contactOpt = leadContactService.getContactById(id);
            
            if (contactOpt.isPresent()) {
                response.put("success", true);
                response.put("data", contactOpt.get());
                response.put("message", "获取联系信息成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("data", null);
                response.put("message", "联系信息不存在");
                return ResponseEntity.status(404).body(response);
            }
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("data", null);
            response.put("message", "获取联系信息失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取默认联系信息
     */
    @GetMapping("/default")
    public ResponseEntity<Map<String, Object>> getDefaultContact() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            var contactOpt = leadContactService.getDefaultContact();
            
            if (contactOpt.isPresent()) {
                response.put("success", true);
                response.put("data", contactOpt.get());
                response.put("message", "获取默认联系信息成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("data", null);
                response.put("message", "未设置默认联系信息");
                return ResponseEntity.status(404).body(response);
            }
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("data", null);
            response.put("message", "获取默认联系信息失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 创建新的联系信息
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createContact(@RequestBody LeadContact contact) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            LeadContact savedContact = leadContactService.saveContact(contact);
            
            response.put("success", true);
            response.put("data", savedContact);
            response.put("message", "创建联系信息成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("data", null);
            response.put("message", "创建联系信息失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 更新联系信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateContact(
            @PathVariable Long id, 
            @RequestBody LeadContact contact) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            var existingContactOpt = leadContactService.getContactById(id);
            
            if (existingContactOpt.isPresent()) {
                contact.setId(id);
                LeadContact updatedContact = leadContactService.saveContact(contact);
                
                response.put("success", true);
                response.put("data", updatedContact);
                response.put("message", "更新联系信息成功");
                
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("data", null);
                response.put("message", "联系信息不存在");
                
                return ResponseEntity.status(404).body(response);
            }
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("data", null);
            response.put("message", "更新联系信息失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 删除联系信息
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteContact(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            leadContactService.deleteContact(id);
            
            response.put("success", true);
            response.put("data", null);
            response.put("message", "删除联系信息成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("data", null);
            response.put("message", "删除联系信息失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 设置默认联系信息
     */
    @PutMapping("/{id}/set-default")
    public ResponseEntity<Map<String, Object>> setDefaultContact(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            leadContactService.setDefaultContact(id);
            
            response.put("success", true);
            response.put("data", null);
            response.put("message", "设置默认联系信息成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("data", null);
            response.put("message", "设置默认联系信息失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(response);
        }
    }



}
