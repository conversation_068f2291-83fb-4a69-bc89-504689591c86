-- 缩略图优化脚本
-- 提升缩略图加载速度的数据库优化

-- 查看当前视频数据
SELECT 
    id as '视频ID',
    title as '标题',
    video_url as '视频URL',
    thumbnail_url as '当前缩略图URL',
    created_time as '创建时间'
FROM videos 
ORDER BY id;

-- 方案1: 使用默认OSS缩略图（推荐 - 最快加载）
UPDATE `videos` 
SET `thumbnail_url` = 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png',
    `updated_time` = NOW()
WHERE `thumbnail_url` IS NULL OR `thumbnail_url` = '';

-- 方案2: 使用压缩版缩略图
-- 假设OSS上有压缩版本，文件名添加_thumb后缀
UPDATE `videos` 
SET `thumbnail_url` = CONCAT(
    SUBSTRING_INDEX(video_url, '.', 1),
    '_thumb.jpg'
),
`updated_time` = NOW()
WHERE `thumbnail_url` IS NULL OR `thumbnail_url` = '';

-- 性能优化索引
CREATE INDEX IF NOT EXISTS idx_videos_thumbnail_url ON videos(thumbnail_url);
CREATE INDEX IF NOT EXISTS idx_videos_active_created ON videos(is_active, created_time DESC);

-- 验证缩略图URL状态
SELECT 
    id,
    title,
    CASE 
        WHEN thumbnail_url IS NULL THEN '❌ 缩略图为空'
        WHEN thumbnail_url = '' THEN '❌ 缩略图为空字符串'
        WHEN thumbnail_url LIKE 'https://jyqk.oss-cn-guangzhou.aliyuncs.com%' THEN '✅ 使用OSS地址'
        WHEN thumbnail_url LIKE 'http%' THEN '⚠️ 其他HTTP地址'
        ELSE '❌ 无效URL格式'
    END as '缩略图状态',
    thumbnail_url
FROM videos
ORDER BY id;
