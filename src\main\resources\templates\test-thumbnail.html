<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缩略图测试页面</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/index-style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-play-circle me-2"></i>缩略图测试
            </a>
            <a href="/" class="btn btn-outline-light btn-sm">
                <i class="fas fa-home me-1"></i>返回首页
            </a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container my-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-image me-2"></i>缩略图测试页面
                </h1>
                
                <!-- 测试说明 -->
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>测试说明</h5>
                    <p class="mb-2">此页面用于测试视频缩略图的显示效果：</p>
                    <ul class="mb-0">
                        <li>✅ 使用OSS地址：<code>https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png</code></li>
                        <li>✅ 支持错误回退：图片加载失败时自动使用默认OSS图片</li>
                        <li>✅ 无链接装饰：<code>text-decoration-none</code>类已应用</li>
                        <li>✅ 响应式设计：适配不同屏幕尺寸</li>
                    </ul>
                </div>

                <!-- 缩略图测试区域 -->
                <div class="row g-4" th:if="${videos != null and !videos.isEmpty()}">
                    <div class="col-lg-3 col-md-4 col-sm-6" th:each="video : ${videos}">
                        <div class="card h-100 shadow-sm">
                            <a th:href="@{/play/{id}(id=${video.id})}" class="video-thumbnail text-decoration-none">
                                <img th:src="${video.thumbnailUrl != null ? video.thumbnailUrl : 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png'}"
                                     class="card-img-top thumbnail-optimized"
                                     th:alt="${video.title}"
                                     loading="lazy"
                                     onerror="this.src='https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png'"
                                     style="height: 200px; object-fit: cover;"
                                     onload="this.classList.add('loaded')"
                                     data-loaded="false">
                            </a>
                            <div class="card-body">
                                <h5 class="card-title" th:text="${video.title}">视频标题</h5>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</small>
                                    <span class="badge bg-primary" th:if="${video.duration != null}" th:text="${video.duration + 's'}">120s</span>
                                </div>
                                
                                <!-- 缩略图URL信息 -->
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>缩略图URL：</strong><br>
                                        <span th:text="${video.thumbnailUrl != null ? video.thumbnailUrl : '使用默认OSS图片'}" 
                                              class="font-monospace" style="font-size: 0.75rem; word-break: break-all;">
                                            URL地址
                                        </span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 无视频提示 -->
                <div th:if="${videos == null or videos.isEmpty()}" class="text-center py-5">
                    <i class="fas fa-video fa-3x text-muted mb-3"></i>
                    <h3 class="text-muted">暂无视频数据</h3>
                    <p class="text-muted">请先添加一些视频来测试缩略图功能</p>
                    <a href="/admin/add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>添加视频
                    </a>
                </div>

                <!-- 默认缩略图测试 -->
                <div class="row mt-5">
                    <div class="col-12">
                        <h3><i class="fas fa-image me-2"></i>默认缩略图测试</h3>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="card">
                                    <img src="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png" 
                                         class="card-img-top" 
                                         alt="默认缩略图"
                                         style="height: 200px; object-fit: cover;">
                                    <div class="card-body">
                                        <h6 class="card-title">默认OSS缩略图</h6>
                                        <small class="text-muted">直接使用OSS地址</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <img src="https://invalid-url.com/nonexistent.jpg" 
                                         class="card-img-top" 
                                         alt="错误回退测试"
                                         style="height: 200px; object-fit: cover;"
                                         onerror="this.src='https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png'">
                                    <div class="card-body">
                                        <h6 class="card-title">错误回退测试</h6>
                                        <small class="text-muted">无效URL，应显示默认图片</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <a href="#" class="video-thumbnail text-decoration-none">
                                        <img src="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png" 
                                             class="card-img-top" 
                                             alt="链接样式测试"
                                             style="height: 200px; object-fit: cover;">
                                    </a>
                                    <div class="card-body">
                                        <h6 class="card-title">链接样式测试</h6>
                                        <small class="text-muted">应无下划线装饰</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-play-circle me-2"></i>缩略图测试</h5>
                    <p class="mb-0">验证OSS缩略图功能的测试页面。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 佳茵轻康. <a href="/about" class="text-light text-decoration-none">关于我们</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/thumbnail-simple.js"></script>
    <script src="/js/main.js"></script>
</body>
</html>
