/**
 * 视频播放页面专用样式
 * Video Play Page Styles
 */

/* 原生HTML5视频播放器样式 - 固定尺寸 270px × 380px */
#video-player {
    width: 270px !important;
    height: 380px !important;
    background-color: #000;
    object-fit: cover;
    border-radius: 8px;
    max-width: none !important;
    max-height: none !important;
    border: 2px solid #ffffff;
}



/* 播放器容器 */
.video-player-container {
    position: relative;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    margin-bottom: 1.5rem;
    width: 270px;
    height: 380px;
    margin-left: auto;
    margin-right: auto;
    border: 2px solid #ffffff;
}

.video-wrapper {
    position: relative;
    width: 270px;
    height: 380px;
    background: #000;
    margin: 0 auto;
}

.video-wrapper .video-js {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* 视频信息区域 */
.video-info {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 1px 0px !important;
}

/* 覆盖Bootstrap的mb-4类，确保margin设置生效 */
.video-info.bg-white.rounded-3.shadow-sm.p-4.mb-4 {
    margin: 1px 0px !important;
}

/* 视频标题 */
.video-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    text-align: center;
}

/* 特定选择器：确保video-title h3 mb-3的内容居中 */
.video-title.h3.mb-3 {
    text-align: center !important;
}









/* 全屏按钮 */
.fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    z-index: 1000;
    transition: background 0.3s ease;
}

.fullscreen-btn:hover {
    background: rgba(0,0,0,0.9);
}

/* 响应式设计 - 保持固定尺寸 */
@media (max-width: 768px) {
    /* 移动端仍保持固定尺寸 */
    .video-player-container,
    .video-wrapper,
    #video-player {
        width: 270px !important;
        height: 380px !important;
    }

    .video-info {
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .video-title {
        font-size: 1.25rem;
    }




}

/* 加载状态 */
.video-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    background: #f8f9fa;
    border-radius: 8px;
}

.video-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 错误状态 */
.video-error {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

.video-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dc3545;
}





/* 响应式优化 */
@media (max-width: 991.98px) {


    /* 确保播放器保持固定尺寸 */
    .video-wrapper {
        width: 270px;
        height: 380px;
        padding-bottom: 0;
    }
}

@media (max-width: 576px) {
    /* 小屏幕设备仍保持固定尺寸，但居中显示 */
    .video-player-container {
        width: 270px !important;
        height: 380px !important;
        margin-left: auto;
        margin-right: auto;
        border-radius: 12px;
    }
    

    .video-wrapper,
    #video-player {
        width: 270px !important;
        height: 380px !important;
    }




}

/* 主容器样式调整 */
.container.my-4 {
    margin-top: 10px !important;
}

/* 页脚样式调整 */
.bg-dark.text-light.py-4.mt-5 {
    margin-top: 5px !important;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .video-info {
        background: #2d3748;
        color: #e2e8f0;
    }

    .video-title {
        color: #f7fafc;
    }


}
