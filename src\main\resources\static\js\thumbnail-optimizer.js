/**
 * 缩略图优化加载器（简化版）
 * 提供基本的懒加载和错误处理功能
 */

class ThumbnailOptimizer {
    constructor() {
        this.imageCache = new Map();
        this.observer = null;
        this.defaultImage = 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png';
        this.init();
    }

    /**
     * 初始化优化器
     */
    init() {
        this.setupIntersectionObserver();
        this.preloadDefaultImage();
        this.setupErrorHandling();
        this.optimizeExistingImages();
    }

    /**
     * 设置交叉观察器（懒加载）
     */
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '100px 0px',
                threshold: 0.1
            });
        }
    }

    /**
     * 预加载默认图片
     */
    preloadDefaultImage() {
        this.preloadImage(this.defaultImage);
    }

    /**
     * 预加载图片
     */
    preloadImage(src) {
        if (this.imageCache.has(src)) {
            return Promise.resolve(this.imageCache.get(src));
        }

        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.imageCache.set(src, img);
                resolve(img);
            };
            img.onerror = reject;
            img.src = src;
        });
    }

    /**
     * 优化现有图片
     */
    optimizeExistingImages() {
        const images = document.querySelectorAll('.thumbnail-optimized');
        images.forEach(img => {
            this.setupImageOptimization(img);
        });
    }

    /**
     * 设置单个图片优化
     */
    setupImageOptimization(img) {
        // 设置初始状态
        if (!img.classList.contains('loaded')) {
            img.style.opacity = '0.7';
        }

        // 如果图片在视口内，立即加载
        if (this.isInViewport(img)) {
            this.loadImage(img);
        } else if (this.observer) {
            // 否则使用懒加载
            this.observer.observe(img);
        } else {
            // 降级处理：直接加载
            this.loadImage(img);
        }
    }

    /**
     * 检查元素是否在视口内
     */
    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * 加载图片
     */
    loadImage(img) {
        if (img.dataset.loaded === 'true' || img.classList.contains('loaded')) {
            return;
        }

        const src = img.src;
        if (!src) return;

        // 设置加载完成处理
        const handleLoad = () => {
            img.classList.add('loaded');
            img.dataset.loaded = 'true';
            img.style.opacity = '1';
            this.imageCache.set(src, true);
        };

        const handleError = () => {
            this.handleImageError(img);
        };

        // 如果图片已经加载完成
        if (img.complete && img.naturalHeight !== 0) {
            handleLoad();
        } else {
            // 添加事件监听器
            img.addEventListener('load', handleLoad, { once: true });
            img.addEventListener('error', handleError, { once: true });
        }
    }

    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        document.addEventListener('error', (e) => {
            if (e.target.tagName === 'IMG' && e.target.classList.contains('thumbnail-optimized')) {
                this.handleImageError(e.target);
            }
        }, true);
    }

    /**
     * 处理图片加载错误
     */
    handleImageError(img) {
        if (img.src !== this.defaultImage) {
            img.src = this.defaultImage;
            img.classList.add('loaded', 'error');
            img.dataset.loaded = 'true';
            img.style.opacity = '1';
        }
    }

    /**
     * 清理资源
     */
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
        this.imageCache.clear();
    }

    /**
     * 获取缓存统计
     */
    getCacheStats() {
        return {
            cacheSize: this.imageCache.size
        };
    }
}

// 全局实例
let thumbnailOptimizer = null;

/**
 * 初始化缩略图优化器
 */
function initThumbnailOptimizer() {
    if (!thumbnailOptimizer) {
        thumbnailOptimizer = new ThumbnailOptimizer();
    }
    return thumbnailOptimizer;
}

/**
 * 优化新添加的图片
 */
function optimizeNewThumbnails(container = document) {
    if (thumbnailOptimizer) {
        const newImages = container.querySelectorAll('.thumbnail-optimized:not([data-optimized])');
        newImages.forEach(img => {
            img.dataset.optimized = 'true';
            thumbnailOptimizer.setupImageOptimization(img);
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initThumbnailOptimizer();
    
    // 监听动态内容变化
    if ('MutationObserver' in window) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            optimizeNewThumbnails(node);
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
});

// 导出到全局
window.ThumbnailOptimizer = ThumbnailOptimizer;
window.initThumbnailOptimizer = initThumbnailOptimizer;
window.optimizeNewThumbnails = optimizeNewThumbnails;
