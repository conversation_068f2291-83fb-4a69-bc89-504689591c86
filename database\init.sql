-- 视频播放器数据库初始化脚本
-- 数据库版本: MySQL 8.0+
-- 字符集: UTF-8

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `video_player` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `video_player`;

-- 创建视频表
CREATE TABLE IF NOT EXISTS `videos` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '视频ID',
    `title` VARCHAR(200) NOT NULL COMMENT '视频标题',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '视频描述',
    `video_url` VARCHAR(1000) NOT NULL COMMENT '视频URL地址',
    `thumbnail_url` VARCHAR(1000) DEFAULT NULL COMMENT '缩略图URL',
    `duration` INT DEFAULT NULL COMMENT '视频时长（秒）',
    `file_size` BIGINT DEFAULT NULL COMMENT '文件大小（字节）',
    `video_format` VARCHAR(20) DEFAULT NULL COMMENT '视频格式',
    `resolution` VARCHAR(20) DEFAULT NULL COMMENT '分辨率',

    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_title` (`title`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频信息表';

-- 插入示例数据（阿里云OSS视频链接示例）
INSERT INTO `videos` (`title`, `description`, `video_url`, `thumbnail_url`, `duration`, `video_format`, `resolution`) VALUES
('示例视频1', '这是一个示例视频，展示如何播放阿里云OSS存储的视频', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/videos/sample1.mp4', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/thumbnails/sample1.jpg', 120, 'mp4', '1080p'),
('示例视频2', '另一个示例视频，支持高清播放', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/videos/sample2.mp4', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/thumbnails/sample2.jpg', 180, 'mp4', '720p'),
('测试视频', '用于测试播放器功能的视频文件', 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/videos/test.mp4', NULL, 60, 'mp4', '480p');



-- 显示创建结果
SHOW TABLES;
SELECT COUNT(*) as video_count FROM videos;

-- 创建联系信息表
CREATE TABLE IF NOT EXISTS `lead_contacts` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '联系信息ID',
    `contact_name` VARCHAR(100) DEFAULT NULL COMMENT '联系人姓名',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '电话号码',
    `wechat` VARCHAR(100) DEFAULT NULL COMMENT '微信号',
    `douyin` VARCHAR(100) DEFAULT NULL COMMENT '抖音号',
    `douyin_nickname` VARCHAR(100) DEFAULT NULL COMMENT '抖音昵称',
    `contact_type` VARCHAR(50) DEFAULT NULL COMMENT '联系类型',
    `is_default` BOOLEAN DEFAULT FALSE COMMENT '是否为默认联系方式',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_contact_name` (`contact_name`),
    INDEX `idx_phone` (`phone`),
    INDEX `idx_is_default` (`is_default`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系信息表';

-- 插入默认联系信息数据
INSERT INTO `lead_contacts` (`contact_name`, `phone`, `wechat`, `douyin`, `douyin_nickname`, `contact_type`, `is_default`, `sort_order`) VALUES
('林佳', '18722880704', '18722880704', '黄林佳', '皮皮管理', '主管', TRUE, 1),
('黄超', '18057722960', '18057722960', '黄超(黄小燕弟弟)', '黄超', '主管', FALSE, 2),
('小班', '15908542510', '15908542510', '佳茵轻康SOS小班', '小班', '客服', FALSE, 3);

-- 数据库初始化完成
SELECT '数据库初始化完成！' as message;
SELECT '联系信息表创建完成！' as contact_message;
SELECT COUNT(*) as contact_count FROM lead_contacts;

